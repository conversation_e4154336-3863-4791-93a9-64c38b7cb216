importScripts('https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js');
importScripts('https://www.gstatic.com/firebasejs/8.10.0/firebase-messaging.js');



const firebaseConfig = {
    apiKey: "AIzaSyAET3BHr7hOU2pyn1Fsym9W6B6A0Xq6v5E",
    authDomain: "sharpbuy-f2d5e.firebaseapp.com",
    projectId: "sharpbuy-f2d5e",
    storageBucket: "sharpbuy-f2d5e.appspot.com",
    messagingSenderId: "730851601803",
    appId: "1:730851601803:web:0e8f2729ec1f8ce61a938e",
    measurementId: "G-2KBR56PCY3"
  };
  
// Initialize Firebase
firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();





const defaultImageSrc = 'https://cdn.builder.io/api/v1/image/assets/TEMP/8f233170b9a0b207ca87204ab9bc8cb05b0a2242b383001e62b1b36b73dcd332?apiKey=0fa4d73961364689ac60c67128abcc09&&apiKey=0fa4d73961364689ac60c67128abcc09';

// Optional: Handle background messages here.
messaging.onBackgroundMessage(async function (payload) {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);

  // const allClients = await clients.matchAll({
  //   type: 'window',
  //   includeUncontrolled: true
  // });

  // let isWindowFocused = false;
  
  // // Check if any of the clients (open windows/tabs) are focused
  // allClients.forEach(client => {
  //   if (client.focused) {
  //     isWindowFocused = true;
  //   }
  // });

  // // If the window is focused, suppress the notification
  // if (isWindowFocused) {
  //   console.log('[firebase-messaging-sw.js] Window is focused. No notification shown.');
  //   return;
  // }

  // Customize notification here if window is not focused
  const notificationTitle = payload?.notification?.title || 'Background Message Title';
  const notificationOptions = {
    body: payload?.notification?.body || 'Background Message body.',
    icon: defaultImageSrc
  };

  return self.registration.showNotification(notificationTitle, notificationOptions);
});
{"name": "sharpbuy-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@calcom/embed-react": "^1.5.0", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@gsap/react": "^2.1.1", "@hookform/resolvers": "^3.9.0", "@mui/icons-material": "^5.15.19", "@mui/joy": "^5.0.0-beta.48", "@mui/material": "^5.16.5", "@mui/system": "^5.16.5", "@mui/x-data-grid": "^7.11.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.2.1", "@reduxjs/toolkit": "^2.2.5", "@shadcn/ui": "^0.0.4", "@splidejs/react-splide": "^0.7.12", "@splidejs/splide-extension-auto-scroll": "^0.5.3", "@tanstack/react-table": "^8.19.3", "autosuggest-highlight": "^3.3.4", "axios": "^1.7.2", "blob-util": "^2.0.2", "chart.js": "^4.4.4", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "dayjs": "^1.11.12", "docx": "^9.0.2", "downloadjs": "^1.4.7", "embla-carousel-react": "^8.1.8", "file-saver": "^2.0.5", "firebase": "^10.13.0", "formik": "^2.4.6", "framer-motion": "^11.2.10", "gsap": "^3.12.5", "html-to-image": "^1.11.11", "i": "^0.3.7", "init": "^0.1.2", "input-otp": "^1.2.4", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "libphonenumber-js": "^1.11.5", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "moment": "^2.30.1", "next": "^14.2.3", "next-themes": "^0.3.0", "npm": "^10.9.0", "parse-google-place": "^1.3.0", "pdfjs-dist": "^4.5.136", "popmotion": "^11.0.5", "query-string": "^9.1.0", "react": "^18.3.1", "react-calendly": "^4.3.1", "react-chartjs-2": "^5.2.0", "react-countup": "^6.5.3", "react-datepicker": "^7.5.0", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-flags-select": "^2.2.3", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-image-crop": "^11.0.6", "react-ionicons": "^4.2.1", "react-joyride": "^2.8.2", "react-paginate": "^8.2.0", "react-phone-input-2": "^2.15.1", "react-redux": "^9.1.2", "react-tooltip": "^5.27.0", "react18-input-otp": "^1.1.4", "recharts": "^2.12.7", "redux": "^5.0.1", "redux-persist": "^6.0.0", "shadcn-ui": "^0.8.0", "socket.io-client": "^4.7.5", "styled-components": "^6.1.12", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "devDependencies": {"@types/autosuggest-highlight": "^3.2.3", "@types/file-saver": "^2.0.7", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}
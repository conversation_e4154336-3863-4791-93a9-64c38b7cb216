// /** @type {import('next').NextConfig} */
// const nextConfig = {
//   images: {
//     domains: [
//       "media.digikey.com",
//       "applore-dev-projects-2.s3.ap-south-1.amazonaws.com",
//       "applore-dev-projects-1.s3.amazonaws.com",
//       "applore-dev-projects-1.s3.ap-south-1.amazonaws.com",
//       "i.postimg.cc",
//       "cdn.builder.io",
//       "applore-dev-projects-2.s3.amazonaws.com",
//       "sharpbuy-strapi.applore.in",
//       "example.com",
//       'media.digikey.com',
//       "sharpbuy-assets.s3.ap-south-1.amazonaws.com",
//       'sharpbuy-assets.s3.amazonaws.com',
//       "applore-dev-projects-3.s3.amazonaws.com",
//       "applore-dev-projects-3.s3.ap-south-1.amazonaws.com"
//       // Add more domains if needed
//     ],
//   },

//   env: {
//     backendUrl: "https://ill-pink-jay-gown.cyclic.app/api",
//     // backendUrl: "http://localhost:9000/api",
//   },
//   eslint: {
//     // Warning: This allows production builds to successfully complete even if
//     // your project has ESLint errors.
//     ignoreDuringBuilds: true,
//   },
//   typescript: {
//     ignoreBuildErrors: true,
//   },
// };

// module.exports = nextConfig;
/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    // Replacing deprecated 'domains' with 'remotePatterns'
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'media.digikey.com',
      },
      {
        protocol: 'https',
        hostname: 'applore-dev-projects-2.s3.ap-south-1.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'applore-dev-projects-1.s3.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'applore-dev-projects-1.s3.ap-south-1.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'i.postimg.cc',
      },
      {
        protocol: 'https',
        hostname: 'cdn.builder.io',
      },
      {
        protocol: 'https',
        hostname: 'applore-dev-projects-2.s3.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'sharpbuy-strapi.applore.in',
      },
      {
        protocol: 'https',
        hostname: 'example.com',
      },
      {
        protocol: 'https',
        hostname: 'sharpbuy-assets.s3.ap-south-1.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'sharpbuy-assets.s3.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'applore-dev-projects-3.s3.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'applore-dev-projects-3.s3.ap-south-1.amazonaws.com',
      },
      // Add more remote patterns as needed
    ],
  },

  env: {
    backendUrl: "https://ill-pink-jay-gown.cyclic.app/api",
    // backendUrl: "http://localhost:9000/api",
  },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
};

module.exports = nextConfig;

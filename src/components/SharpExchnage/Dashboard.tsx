"use client";
import React from "react";
import { SharpExchangeDashboard } from "../shared/DashboardFactory";
import { useSelector } from "react-redux";
import DashboardCards from "./DashboardCards";
import PendingOverTask from "@/components/SharpExchnage/Seller/PendingOverTask";

const Dashboard = () => {
  const selectedTab = useSelector((state: any) => state.tab.selectedTab);
  const user = useSelector((state: any) => state.user);

  const customContent = (
    <>
      {selectedTab === "seller" && (
        <DashboardCards access={user?.userDetails?.access} />
      )}
      <PendingOverTask />
    </>
  );

  return (
    <SharpExchangeDashboard
      title="Exchange Overview"
      description="Real-time inventory overview, active listings, orders, and analytics."
      customContent={customContent}
    />
  );
};

export default Dashboard;

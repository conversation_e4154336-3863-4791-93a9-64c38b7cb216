'use client'
import React from 'react'
import { useSelector } from 'react-redux'
import { SharpExchangeNavbar } from '../../../shared/NavbarFactory';
import InvoiceInfo from './InvoiceInfo';

const Invoice = () => {
    const rightSidebarOpen=useSelector(
        (state:any) => state.sidebar.rightSidebarOpen
    );

  return (
    <div
      style={{ padding: rightSidebarOpen ? "0 10px" : "0"}}
    >
      <SharpExchangeNavbar title="Transaction Records" description="Manage Your Billing Documents" />
      <div className="w-full   h-[122vh] xl-custom:h-[105vh] flex flex-col gap-4 font-outfitOnly bg-[#0C0C0E] p-[20px] py-5 rounded-r-xl rounded-b-xl">
        <div className="w-full max-h-[45vh] h-full flex flex-col gap-8">
          <InvoiceInfo/>
        </div>
      </div>
    </div>
  )
}

export default Invoice
"use client";
import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useState,
} from "react";
import { useSelector } from "react-redux";
import { SharpExchangeNavbar } from "@/components/shared/NavbarFactory";
import { Box } from "@mui/material";
import DataTable from "@/components/shared/DataTable";
import Image from "next/image";
import ReactPaginate from "react-paginate";
import { GridColDef, GridRowId, GridRowModel } from "@mui/x-data-grid";
import UploadModal from "./UploadModal";
import CustomTable from "@/components/shared/CustomTable";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import UploadFailedModal from "./UploadFailedmodal";
import { callAxios } from "@/utilis/axios";
import { toastMessage } from "@/utilis/toast";
import Link from "next/link";
import { useCustomToast } from "@/components/shared/CustomToast";
import { useRouter } from "next/navigation";
import { DeleteConfirmation } from "@/components/shared/DeleteConfirmation";
import CommonFilters from "@/components/shared/DataFilters";
import SortFilters from "../../DataSorting";
import { convertToUSD } from "@/utilis/ConvertToUSD";
import { convertTargetPrice } from "@/utilis/ConvertCurrency";
import PaginationComp from "@/components/shared/PaginationComp";
import _ from "lodash";
import MissingPriceModal from "./MissingPriceModal";

const Dashboard = () => {
  const rightSidebarOpen = useSelector(
    (state: any) => state.sidebar.rightSidebarOpen
  );
  const selectedTab = useSelector((state: any) => state.tab.selectedTab);
let OnlyReload:any ;
  const user = useSelector((state: any) => state.user);

  const [search, setSearch] = useState("");
  const [totalPages, setTotalPages] = useState(1);
  const [pageNumber, setPageNumber] = useState(1);
  const [showSort, setshowSort] = useState<boolean>(false);
  const [showFilter, setShowFilter] = useState<boolean>(false);
  const [sortOptions, setSortOptions] = useState<
    Record<string, "asc" | "desc" | null>
  >({});
  const [prevSortedOptions, setPrevSortedOptions] = useState<
    Record<string, "asc" | "desc" | null>
  >({});
  const [limit, setlimit] = useState(10);

  const convertSortOptionsToString = (
    sortOptions: Record<string, "asc" | "desc" | null>
  ) => {
    const keys = Object.keys(sortOptions).filter(
      (key) => sortOptions[key] !== null
    );
    const sortOrders = keys.map((key) => sortOptions[key]!);

    // Convert arrays to comma-separated strings and wrap in brackets
    const keysString = JSON.stringify(keys); // Correctly format as JSON array
    const sortOrdersString = JSON.stringify(sortOrders); // Correctly format as JSON array

    // Format the result string
    return `sortBy=${keysString}&sortOrder=${sortOrdersString}`;
  };

  const handleSortChange = (
    newSortOptions: Record<string, "asc" | "desc" | null>
  ) => {
    setPrevSortedOptions(newSortOptions);

    const SORT_OPTION = convertSortOptionsToString(newSortOptions);
    setSortOptions(SORT_OPTION);
  };

  const [rows, setRows] = useState([
    // { id: 1, sno: 1, productDescription: 'Semiconductor aws 1130 core...', partNumber: 'CY8CMBR3110-SX2IT', dateCode: '03-2024', quantity: '40,000 kg', yourTP: '$1200', leadTime: '20', country: "IN", zipCode: '201301' },
    // { id: 2, sno: 2, productDescription: 'Semiconductor aws 1130 core...', partNumber: 'CY8CMBR3110-SX2IT', dateCode: '03-2024', quantity: '40,000 units', yourTP: '$20', leadTime: '20' },
    // { id: 3, sno: 3, productDescription: 'Semiconductor aws 1130 core...', partNumber: 'CY8CMBR3110-SX2IT', dateCode: '03-2024', quantity: '20,000 kg', yourTP: '$20', leadTime: '20' },
    // { id: 4, sno: 4, productDescription: 'Semiconductor aws 1130 core...', partNumber: 'CY8CMBR3110-SX2IT', dateCode: '03-2024', quantity: '20,000 pieces', yourTP: '$20', leadTime: '20' },
    // { id: 5, sno: 5, productDescription: 'Semiconductor aws 1130 core...', partNumber: 'CY8CMBR3110-SX2IT', dateCode: '03-2024', quantity: '20,000 units', yourTP: '$20', leadTime: '20' },
    // { id: 6, sno: 6, productDescription: 'Semiconductor aws 1130 core...', partNumber: 'CY8CMBR3110-SX2IT', dateCode: '03-2024', quantity: '20,000 units', yourTP: '$20', leadTime: '20' },
    // { id: 7, sno: 7, productDescription: 'Semiconductor aws 1130 core...', partNumber: 'CY8CMBR3110-SX2IT', dateCode: '03-2024', quantity: '20,000 feets', yourTP: '$20', leadTime: '20' },
    // { id: 8, sno: 8, productDescription: 'Semiconductor aws 1130 core...', partNumber: 'CY8CMBR3110-SX2IT', dateCode: '03-2024', quantity: '20,000 units', yourTP: '$20', leadTime: '20' },
  ]);
const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState<any>(null);

  const [selectedRow, setSelectedRow] = useState(null);
  const [editRows, setEditRows] = useState<Record<GridRowId, boolean>>({});
  const [UploadPopup, setUploadPopup] = React.useState(false);
  const [updateFailedPopup, setupdateFailedPopup] = React.useState(false);

  const [UploadErrors, setUploadErrors] = useState([]);
  const [selectedrows, setselectedrows] = useState(new Set());
  const [totalInventory, settotalInventory] = useState(0);
  const [deletePoppup, setDeletepopup] = useState(false);
  const [priceMissing, setPriceMissing] = useState(false);

  const handleSearch = (e: any) => {
    setSearch(e.target.value);
    // setPriceMissing(false)
  };
  const handleRowClick = (row: any) => {
    setSelectedRow(row);
  };
  const handlePageClick = (selectedItem: { selected: number }) => {
    console.log("selectedItem",selectedItem)
    
    const PageNO = selectedItem.selected === 0 ? 1 : selectedItem.selected + 1 ;

    setPageNumber(PageNO);

    const newPageNumber = selectedItem.selected + 1;
    setPageNumber(newPageNumber);
 
  };
  
  

  const handleEditClick = (id: number) => {
    setEditRows((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const router = useRouter();
  const selectedCurrency = useSelector(
    (state) => state.currency.selectedCurrency
  );

  const handleFiltersUpdate = (filters: any) => {
    setFilters(filters);
    // Handle the filter data as needed
    console.log("Filters from child:", filters);
  };

  const revertTransformedData = (transformedData) => {
    if (!transformedData) return [];

    console.log("transformedData", transformedData);

    const revertItem = (item) => {
      // Extract numeric value from yourTP by removing the currency symbol
      const numericTargetPrice =
        parseFloat(
          item.yourTP?.replace(selectedCurrency?.symbol, "").replace(/,/g, "")
        ) || 0;

      return {
        product: item.product,
        description: item.productDescription || "",
        productNo: item.partNumber || "",
        dateCode: item.dateCode || "",
        quantity: parseInt(item.quantity?.split(" ")[0]) || 0,
        unitsStock: item.quantity?.split(" ")[1] || "",
        targetPrice: convertToUSD(numericTargetPrice, selectedCurrency),
        leadTime: Number(item.leadTime?.split(" ")[0]) || "",
        location: item.location || "IN",
        country: item.country || "IN",
        pinCode: item.zipCode || "",
      };
    };

    // Check if transformedData is an array or a single object
    if (Array.isArray(transformedData)) {
      return transformedData.map(revertItem);
    } else {
      return revertItem(transformedData);
    }
  };

  const handleProcessRowUpdate = async (newRow, oldRow) => {
    const data = revertTransformedData(newRow);
    await UpdateRow(newRow?._id, data);
    return newRow;
  };

  const UpdateRow = async (id, data) => {
    try {
      await callAxios("put", `app/inventory/editInventoryProduct/${id}`, data);
      debouncedFetchData(
        pageNumber,
        limit,
        filters?.location,
        sortOptions,
        search
      );
    } catch (error) {
      console.error("Error while updating", error);
    }
  };

  const handleSelectionChange = useCallback((selectedRows) => {
    console.log("Selected Rows:", selectedRows);
    setselectedrows(selectedRows);
    if (selectedRows?.length === limit) {
      warning("warning", "You can Select Per Page Rows");
    }
    // Handle delete or other actions with selected rows here
  }, []);

  interface RowData {
    id: number;
    sno: number;
    productDescription: string;
    partNumber: string;
    dateCode: string;
    quantity: string;
    yourTP: string;
    leadTime: string;
    location: string;
  }

  const columns: ColumnDef<RowData>[] = [
    {
      accessorKey: "sno",
      header: "S.No",
      cell: ({ row }) => (
        <div className="text-center">{row.getValue("sno")}</div>
      ),
    },
    {
      accessorKey: "productDescription",
      header: "Product Description",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("productDescription")}</div>
      ),
    },
    {
      accessorKey: "partNumber",
      header: "Part Number",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("partNumber")}</div>
      ),
    },
    {
      accessorKey: "dateCode",
      header: "Date Code",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("dateCode")}</div>
      ),
    },
    {
      accessorKey: "quantity",
      header: "Quantity",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("quantity")}</div>
      ),
    },
    {
      accessorKey: "yourTP",
      header: "Your TP",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("yourTP")}</div>
      ),
    },
    {
      accessorKey: "leadTime",
      header: "Lead Time",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("leadTime")}</div>
      ),
    },
    {
      accessorKey: "location",
      header: "Location",
      cell: ({ row }) => (
        <div className="text-left bg-">{row.getValue("location")}</div>
      ),
    },
    // {
    //   id: "actions",
    //   cell: ({ row }) => (
    //     <Button onClick={() => handleEditClick(row.original.id)}>
    //       {editRows[row.original.id] ? 'Save' : 'Edit'}
    //     </Button>
    //   ),
    // },
    // {
    //   id: "actions",
    //   cell: ({ row }) => {
    //     const rowData = row.original
    //     return (
    //       <Button
    //         variant="ghost"
    //         className="h-8 w-8 p-0"
    //         onClick={() => handleEditClick(rowData.id)}
    //       >
    //         <svg
    //           xmlns="http://www.w3.org/2000/svg"
    //           width="16"
    //           height="17"
    //           viewBox="0 0 16 17"
    //           fill="none"
    //           className="cursor-pointer"
    //         >
    //           <path
    //             d="M9.99935 4.8774L11.9993 6.8774M8.66602 14.2107H13.9993M3.33268 11.5441L2.66602 14.2107L5.33268 13.5441L13.0567 5.82007C13.3066 5.57003 13.4471 5.23095 13.4471 4.8774C13.4471 4.52385 13.3066 4.18477 13.0567 3.93474L12.942 3.82007C12.692 3.57011 12.3529 3.42969 11.9993 3.42969C11.6458 3.42969 11.3067 3.57011 11.0567 3.82007L3.33268 11.5441Z"
    //             stroke="#FFBD2E"
    //             strokeWidth="1.5"
    //             strokeLinecap="round"
    //             strokeLinejoin="round"
    //           />
    //         </svg>
    //       </Button>
    //     )
    //   },
    // },
  ];

  

  const transformData = (data: any[] ,pageNumber:number) => {
    if (!data) return [];

    console.log("item.productNo", data);


    return data.flat().map((item, index) => {
      const convertedTargetPrice = convertTargetPrice(
        item.targetPrice || 0,
        selectedCurrency
      );
      return {
        _id: item._id,
        id: item.product || index,
        sno: ( pageNumber - 1) * 10 + index + 1 ,
        productDescription: item.description || "",
        partNumber: item.productNo || "",
        dateCode: item.dateCode || "",
        quantity: (item.quantity || 0) + " " + (item.unitsStock || ""),
        yourTP:
          selectedCurrency?.symbol + " " + convertedTargetPrice.toFixed(2), // Format to 2 decimal places
        leadTime: item.leadTime || "",
        location: item.country,
        country: item.country || "IN",
        zipCode: item.pinCode || "",
      };
    });
  };



  const fetchData = async (
    pageNumber: number,
    limit: number,
    location: string,
    sortOptions: any,
    searchString?: string,
    NeedtoOpen?:any
  ) => {
    try {
      setLoading(true);
      const response = await callAxios(
        "get",
        `app/inventory/getPublishedInventory?page=${searchString !==""? 1: pageNumber}&search=&limit=${limit}&location=${
          location || ""
        }&${sortOptions}&searchString=${searchString}`
      );
      setTotalPages(response?.data?.totalPages || 1);
      // const transformedData: any = transformData(response?.data?.inventory,pageNumber);
      const transformedData: any = transformData(response?.data?.inventory ,pageNumber);
      settotalInventory(response?.data?.totalInventory);
      setRows(transformedData);
      const NEED_to_OPEN_TP = response?.data?.inventory?.filter(row => row.targetPrice === 0 ||row.targetPrice === undefined ) ;
      console.log("NEED_to_OPEN_TP",NEED_to_OPEN_TP)
      if(NeedtoOpen){
        setPriceMissing(NEED_to_OPEN_TP?.length>0 && pageNumber === 1 && search === "" && (OnlyReload === undefined )  )
        OnlyReload="true"
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setLoading(false);
    }
   
  };

  const debouncedFetchData = useCallback(_.debounce(fetchData, 100), [selectedCurrency]);


  useLayoutEffect(() => {
  
    debouncedFetchData(
      pageNumber,
      limit,
      filters?.location,
      sortOptions,
      search,
      true,
      
    );
  }, [search, pageNumber, filters, sortOptions, limit ,selectedCurrency]);

  const { success, error, warning } = useCustomToast();

  const DeleteSelecteditems = async () => {
    if (selectedrows?.size === 0) {
      warning("message", "Please Select Inventory");
      return;
    }
    const idsArray = Array.from(selectedrows).map((id) => id);

    // const idsArray = selectedrows?.map((row) => row?._id);
    console.log("idsArray", idsArray);

    const EQ = selectedrows?.size === totalInventory;

    if (EQ) {
      setDeletepopup(true); // Show the popup if all rows are selected
    } else {
      // Proceed with deletion if not all rows are selected
      try {
        const response = await callAxios(
          "delete",
          "app/inventory/deletePublishedInventory",
          {
            inventoryIds: idsArray,
            type: "ARRAY",
            deleteType: "PUBLISHED",
          }
        );
        if (response?.status === 200) {
          success("Success", "Inventory Deleted Successfully");
          selectedrows.clear();
          debouncedFetchData(
            pageNumber,
            limit,
            filters?.location,
            sortOptions,
            search
          );
        } else {
          warning("message", response?.data?.message);
        }
      } catch (error) {
        console.error("Error sending data:", error);
      }
    }
  };
  useEffect(() => {
    console.log("selectedrows after deletion:", selectedrows);
  }, [selectedrows]);

  const handleConfirmDelete = async () => {
    try {
      const response = await callAxios(
        "delete",
        "app/inventory/deletePublishedInventory",
        {
          inventoryIds: [],
          type: "BULK",
          deleteType: "PUBLISHED",
        }
      );
      console.log("delete res", response);
      if (response?.status === 200) {
        success("Success", "All Inventory Deleted Successfully");
        selectedrows.clear();
        debouncedFetchData(
          pageNumber,
          limit,
          filters?.location,
          sortOptions,
          search
        );
      } else {
        warning("message", response?.data?.message);
      }
      setDeletepopup(false); // Close the popup after deletion
    } catch (error) {
      console.error("Error sending data:", error);
    }
  };

  const Refetch =()=>{
    fetchData(
      pageNumber,
      limit,
      filters?.location,
      sortOptions,
      search,
      false
    );
    }


    const viewProdDetails = async (productNo: string) => {
      try {
          const response = await callAxios("get", `app/product/productGlobalSearch?search=${productNo}`);
          console.log("search result", response?.data?.result);

          if (response?.data?.result && response.data.result.length > 0) {
              const item = response.data.result.filter((item: any) => item.label === productNo)[0] || response.data.result[0];
              // Assuming the first result is the correct one
              if (item.type === 'product') {
                  // alert(item.type)
                  router.push(`/sharp-exchange/buyer/product-list/1/${item._id}`);
              } else {
                  router.push(`/sharp-exchange/buyer/product-list/${item.id}&${item?.type === "subcategory" ? item?.category : item?._id}`);
              }
          } else {
              warning("message", "No Part found");
              // You might want to show an error message to the user here
          }
      } catch (error) {
          console.error("Error in handleSeeMore:", error);
          // You might want to show an error message to the user here
      }

  }



  return (
    <div style={{ padding: rightSidebarOpen ? "0 10px" : "0" }}>
      <SharpExchangeNavbar
        title="Inventory"
        description="Check out your  inventory and easily add more to trim your surplus stock."
      />
      <div className=" bg-[#0C0C0E] h-[115vh] xl-custom:h-[110vh] rounded-lg w-[99%] p-5 flex flex-col gap-5 hidden-overflow-x">
        <div className="mt-[-20px]">
          <div className="step-text">
            <span className="pr-1">Step 1 :</span> Upload inventory
          </div>
          <div className="step-text">
            <span className="pr-1">Step 2 :</span> Correct the unmatched parts
          </div>
          <div className="step-text">
            <span className="pr-1">Step 3 :</span> Publish the matched inventory
            to list for bids
          </div>
        </div>

        <div className="h-[113px] justify-start items-start gap-6 inline-flex">
          <div className="justify-start items-start gap-[31px] flex">
            <div className="justify-start items-start gap-[31px] flex">
              {(user?.userDetails?.type === "PRIMARY_CONTACT" ||
                user?.userDetails?.type === "APPROVING_AUTHORITY" ||
                (user?.userDetails?.type === "USER" &&
                  user?.userDetails?.access !== "VIEW" &&
                  user?.userDetails?.access !== "UPDATE")) && (
                <div
                  className="w-[315px] h-[113px] p-5 bg-[#18181c] rounded-md border border-white/20 justify-start items-center gap-[22px] flex cursor-pointer"
                  onClick={() => setUploadPopup(true)}
                >
                  <div className="w-9 h-9 relative">
                    <img
                      loading="lazy"
                      src="https://cdn.builder.io/api/v1/image/assets/TEMP/6ca905eb7be02d04713abb8fa4c7bc87e3fefb9ea5cd87b530d8eb5e83f3c366?"
                      className="shrink-0 my-auto w-9 aspect-square"
                    />
                  </div>

                  <div className=" addMore flex-col justify-start items-start gap-1.5 inline-flex">
                    <div className="text-[#c4c4c4] text-lg font-medium font-['Outfit']">
                      Add More Inventory
                    </div>
                    <div className="text-[#c4c4c4] text-[13px] font-light font-['Outfit']">
                      XLXS | Maximum File Size: 5 MB
                    </div>
                  </div>
                </div>
              )}

              <Link href={`/sharp-exchange/seller/inventory/ReviewInventory`}>
                <div className=" reviewUnPublished w-[315px] h-[113px] p-5 bg-[#18181c] rounded-md border border-white/20 justify-start items-center gap-[22px] flex">
                  <div className="w-9 h-9 relative">
                    <img
                      loading="lazy"
                      src="/assets/icons/review.svg"
                      className="shrink-0 my-auto w-9 aspect-square"
                    />
                  </div>
                  <div className="w-[167px] flex-col justify-start items-start gap-1 inline-flex">
                    <div className="w-[167px] text-[#c4c4c4] text-lg font-medium font-['Outfit']">
                      Review Unpublished Inventory
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          </div>
        </div>

        <div className=" flex justify-between w-full">
          <div className=" PublishedInventory text-neutral-100 text-xl font-semibold font-['Outfit']">
            Published Inventory
          </div>

          <div
            className="flex gap-5 justify-between self-start "
            style={{ display: selectedrows?.size === 0 && "none" }}
          >
            <div
              onClick={DeleteSelecteditems}
              className="px-3 py-1.5 cursor-pointer text-red-500 rounded-md border border-red-500 border-solid max-md:px-5"
            >
              Delete ({selectedrows?.size})
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-2 ">
          {UploadPopup && (
            <UploadModal
              close={() => setUploadPopup(false)}
              setupdateFailedPopup={setupdateFailedPopup}
              setUploadErrors={setUploadErrors}
            />
          )}
          {updateFailedPopup && (
            <UploadFailedModal
              close={() => setupdateFailedPopup(false)}
              data={UploadErrors}
            />
          )}
          {deletePoppup && (
            <DeleteConfirmation
              title="Are you sure you want to delete all items?"
              setpopupOpen={setDeletepopup}
              handleConfirmDelete={handleConfirmDelete} // Pass the confirm delete handler
            />
          )}

          <div className="flex justify-between">
            <div className="w-full">
              {
                <div className="w-80 h-[50px] px-4 py-2.5 rounded border border-white/opacity-40 flex justify-between items-center gap-2.5">
                  <div className="w-6 h-6 flex justify-center items-center">
                    <Image
                      src="/assets/icons/search.svg"
                      alt="Search"
                      width={25}
                      height={25}
                    />
                  </div>
                  <input
                    type="text"
                    value={search}
                    onChange={handleSearch}
                    className="w-full bg-transparent text-stone-300 text-md font-normal outfit leading-tight focus:outline-none"
                    placeholder="Search"
                  />
                </div>
              }
            </div>

            <div className="flex flex-row-reverse	 items-center gap-6 mr-4">
              <div
                onClick={() => {
                  setshowSort(true);
                  setShowFilter(false);
                }}
                className="w-[100px] flex items-center justify-center gap-2 border rounded-sm border-[rgba(71, 70, 70, 0.54)] p-2.5 cursor-pointer"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M4.5 7H19.5M4.5 12H14.5M4.5 17H8.5"
                    stroke="#C4C4C4"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span>Sort</span>
              </div>
              <div
                onClick={() => {
                  setShowFilter(true);
                  setshowSort(false);
                }}
                className="w-[100px] flex items-center justify-center gap-2 border rounded-sm border-[rgba(71, 70, 70, 0.54)] p-2.5 cursor-pointer"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M21.25 12.0018H8.895M4.534 12.0018H2.75M4.534 12.0018C4.534 11.4237 4.76368 10.8692 5.17251 10.4604C5.58134 10.0515 6.13583 9.82184 6.714 9.82184C7.29217 9.82184 7.84666 10.0515 8.25549 10.4604C8.66432 10.8692 8.894 11.4237 8.894 12.0018C8.894 12.58 8.66432 13.1345 8.25549 13.5433C7.84666 13.9522 7.29217 14.1818 6.714 14.1818C6.13583 14.1818 5.58134 13.9522 5.17251 13.5433C4.76368 13.1345 4.534 12.58 4.534 12.0018ZM21.25 18.6088H15.502M15.502 18.6088C15.502 19.1871 15.2718 19.7423 14.8628 20.1512C14.4539 20.5601 13.8993 20.7898 13.321 20.7898C12.7428 20.7898 12.1883 20.5592 11.7795 20.1503C11.3707 19.7415 11.141 19.187 11.141 18.6088M15.502 18.6088C15.502 18.0305 15.2718 17.4764 14.8628 17.0675C14.4539 16.6586 13.8993 16.4288 13.321 16.4288C12.7428 16.4288 12.1883 16.6585 11.7795 17.0674C11.3707 17.4762 11.141 18.0307 11.141 18.6088M11.141 18.6088H2.75M21.25 5.39484H18.145M13.784 5.39484H2.75M13.784 5.39484C13.784 4.81667 14.0137 4.26218 14.4225 3.85335C14.8313 3.44452 15.3858 3.21484 15.964 3.21484C16.2503 3.21484 16.5338 3.27123 16.7983 3.38079C17.0627 3.49034 17.3031 3.65092 17.5055 3.85335C17.7079 4.05578 17.8685 4.2961 17.9781 4.56059C18.0876 4.82508 18.144 5.10856 18.144 5.39484C18.144 5.68113 18.0876 5.9646 17.9781 6.22909C17.8685 6.49358 17.7079 6.7339 17.5055 6.93634C17.3031 7.13877 17.0627 7.29935 16.7983 7.4089C16.5338 7.51846 16.2503 7.57484 15.964 7.57484C15.3858 7.57484 14.8313 7.34517 14.4225 6.93634C14.0137 6.52751 13.784 5.97302 13.784 5.39484Z"
                    stroke="#C4C4C4"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                  />
                </svg>
                <span>Filter</span>
              </div>
              <div>
                <PaginationComp
                  limit={limit}
                  length={totalInventory}
                  setlimit={setlimit}
                />
              </div>
            </div>
          </div>

          {showFilter && (
            <div className="absolute z-50 p-2 right-10">
              <CommonFilters
                onClose={() => setShowFilter(false)}
                onApplyFilters={handleFiltersUpdate}
                type="PUBLISHED"
                 page="inventory"
              />
            </div>
          )}

          {showSort && (
            <div className="absolute z-50 p-2 right-10">
              <SortFilters
                keys={["quantity", "leadTime", "targetPrice"]}
                prevSortedOptions={prevSortedOptions}
                onSortChange={handleSortChange}
                onClose={() => setshowSort(false)}
               
              />
            </div>
          )}

          <div className="permissionTable  w-full max-w-full overflow-scroll !important">
            <Box sx={{ width: "100%" ,  maxWidth: "100%", }}>
              {rows?.length > 0 ? (
                <CustomTable
                  columns={columns}
                  rows={rows}
                  handleProcessRowUpdate={handleProcessRowUpdate}
                  editRows={editRows}
                  Showtooltip={false}
                  viewProdDetails={()=>viewProdDetails()}
                  searchValue={search}
                  editable={user?.userDetails?.access !== "VIEW"}
                  multipleSelectable={user?.userDetails?.access !== "VIEW"}
                  onSelectionChange={handleSelectionChange}
                  dataLength={totalInventory}
                  pageNumber={pageNumber}
                  limit={limit}
                />
              ) : (
                <>
                {loading  ? <div className=" w-full min-h-[600px] text-2xl flex flex-col gap-6 items-center justify-center">Loading...</div> : <div className="h-full w-full flex mt-10 justify-center items-center">
                  <div className="flex flex-col gap-[30px] justify-center items-center ">
                    <Image
                      src="/assets/NoinventoryUpload.svg"
                      width={120}
                      height={120}
                      alt="no inventory"
                    />
                    <h1 className="text-center font-outfitOnly font-bold text-2xl opacity-90 leading-6 text-[#c4c4c4]">
                      No Inventory Uploaded Yet!
                    </h1>
                    <p className="font-outfitOnly text-lg w-[60%] font-light opacity-90 text-center text-[#c4c4c4]">
                      It looks like you haven't uploaded any inventory items
                      yet. Start by adding your first item to keep track of your
                      inventory effortlessly.
                    </p>
                  </div>
                </div>}
                </>
              )}
            </Box>
            {priceMissing && <MissingPriceModal onClose={()=>{setPriceMissing(false);Refetch()}}  />}
          </div>

          {(rows?.length >= 9 || pageNumber > 1) && (
            <ReactPaginate
              previousLabel={
                <div className="flex items-center gap-4 cursor-pointer">
                  <img
                    src="/assets/icons/arrow_forward.svg"
                    alt="Arrow Forward"
                    className="Get_Started_arrow transform rotate-180"
                  />
                  <p>Previous</p>
                </div>
              }
              nextLabel={
                <div className="flex items-center gap-4 cursor-pointer">
                  <p>Next</p>
                  <img
                    src="/assets/icons/arrow_forward.svg"
                    alt="Arrow Forward"
                    className="Get_Started_arrow transform-[rotate(45deg)]"
                  />
                </div>
              }
              breakLabel={"..."}
              breakClassName={"break-me"}
              pageCount={totalPages}
              marginPagesDisplayed={2}
              pageRangeDisplayed={5}
              onPageChange={handlePageClick}
              containerClassName="flex justify-around items-center px-4 py-2"
              previousLinkClassName="px-4 py-2 text-white"
              nextLinkClassName="px-4 py-2 text-white"
              pageClassName="flex items-center justify-center w-10 h-10 rounded-md cursor-pointer"
              pageLinkClassName="flex items-center justify-center w-full h-full"
              activeClassName="bg-stone-900  px-2 py-2 text-white"
              activeLinkClassName="font-bold"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;


"use client";

import { useEffect } from "react";

const ServiceWorkerRegistration = () => {
  useEffect(() => {
    if (typeof window !== "undefined" && typeof navigator !== "undefined" &&  "serviceWorker" in navigator) {
      const registerServiceWorker = async () => {
        try {
          const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
          console.log('Service Worker registered with scope:', registration.scope);
        } catch (error) {
          console.error('Error registering Service Worker:', error);
        }
      };

      registerServiceWorker();
    }
  }, []);

  return null;
};

export default ServiceWorkerRegistration;

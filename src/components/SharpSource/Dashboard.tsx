"use client"
import React from 'react';
import { SharpSourceDashboard } from '../shared/DashboardFactory';
import { useSelector } from 'react-redux';
import DashboardCards from './DashboardCards';
import PendingOverTask from './seller/PendingOverTasks';

const Dashboard = () => {
  const selectedTab = useSelector((state: any) => state.tab.selectedTab);
  const user = useSelector((state: any) => state.user);

  const customContent = (
    <>
      {selectedTab === "seller" && (
        <DashboardCards access={user?.userDetails?.access} />
      )}
      <PendingOverTask />
    </>
  );

  return (
    <SharpSourceDashboard
      title="Source Overview"
      description="Your smart, seamless solution for selling electronic components"
      customContent={customContent}
    />
  );
};

export default Dashboard;

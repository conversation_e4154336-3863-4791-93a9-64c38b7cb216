"use client";
import { SharpSourceNavbar } from "@/components/shared/NavbarFactory";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import AddAddress from "./AddAddress";
import { callAxios } from "@/utilis/axios";
import toast from "react-hot-toast";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import EditAddress from "./EditAddress";
import { useCustomToast } from "@/components/shared/CustomToast";

const Address = () => {
  const query = useSearchParams();
  const id = query.get("id");
  const router = useRouter();
  const [isEditPopupOpen, setIsEditPopupOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);
  const [isAddPopupOpen, setIsAddPopupOpen] = useState(false);
  const [addresses, setAddresses] = useState<any>({});
  const [selectedAddress, setSelectedAddress] = useState<any>(null);
  const [sellerPopUp, setSellerPopUp] = useState(false);
  const [consignmentDetail, setConsignmentDetail] = useState({
    length: "",
    breadth: "",
    height: "",
    weight: "",
    quantity: "",
  });
  const [errors, setErrors] = useState({
    length: "",
    breadth: "",
    height: "",
    weight: "",
    quantity: "",
  });
  const { success, error, warning } = useCustomToast();
  const rightSidebarOpen = useSelector(
    (state: any) => state.sidebar.rightSidebarOpen
  );
  const pathName = usePathname();
  const type = pathName.includes("/seller") ? "SELLER" : "BUYER";
  const path = type === "BUYER" ? "buyer" : "seller";

  const getUserAddress = async () => {
    try {
      const res = await callAxios("get", "app/user/getLoggedInUserDetails");
      console.log("data", res.data);
      setAddresses(res?.data);
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    getUserAddress();
  }, [loading]);

  const extractCountryFromAddress = (address: any) => {
    const parts = address.split(",").map((part: any) => part.trim());
    const country = parts.pop();
    return country || "Country not found";
  };
  const handleSaveAddress = async (updatedAddress: Address) => {
    try {
      const countryFromAddress = extractCountryFromAddress(
        updatedAddress.address
      );
      const payload = {
        name: updatedAddress.name,
        address: updatedAddress.address,
        countryCode: updatedAddress.countryCode,
        address2: updatedAddress.address2,
        email: updatedAddress.email,
        country: updatedAddress.country,
        phone: updatedAddress.phone,
        city: updatedAddress.city,
        state: updatedAddress.state,
        pincode: updatedAddress.pincode,
        // country: countryFromAddress,
      };
      console.log("pauload", payload);
      const addressId = updatedAddress._id;
      console.log(addressId);

      const response = await callAxios(
        "put",
        `app/user/updateAddress/${addressId}`,
        payload as any
      );

      console.log(response);
      success("Success", "Address Updated Successfully!");
      handleClosePopup();
      await getUserAddress();
    } catch (err) {
      console.error("Error adding address:", err);
      error("Error", "Something went wrong!");
    }
  };

  const handleClosePopup = () => {
    setIsEditPopupOpen(false);
    setEditingAddress(null);
    setIsAddPopupOpen(false);
  };
  const handleDeleteAddress = async (address: { _id: string } | string) => {
    // console.log("Received address:", address);
    // debugger
    let deleteId: string;

    if (typeof address === "string") {
      deleteId = address;
    } else if (address && typeof address === "object" && "_id" in address) {
      deleteId = address._id;
    } else {
      console.error("Invalid address format:", address);
      return;
    }

    console.log("Deleting address with ID:", deleteId);

    try {
      const response = await callAxios(
        "delete",
        `app/user/deleteAddress/${deleteId}`
      );

      success("Success", "Address Deleted Successfully!");
      console.log("Delete response:", response);

      handleClosePopup();

      await getUserAddress();
    } catch (err) {
      error("Error", "Something went wrong!");
      console.error("Error deleting address:", err);
    }
  };

  const handleAddressSelection = (item: any) => {
    setSelectedAddress(item);
  };

  const choosedAddress = async () => {
    try {
      if (!selectedAddress) {
        toast.error("Please choose one address");
      } else {
        const payload = {
          order: id,
          address: selectedAddress?._id,
          type: type,
        };
        if (type === "BUYER") {
          const res = await callAxios(
            "post",
            // "app/order/addShippingDetails",
            `sharp-source/app/source-order/addSellerShippingDetail/${id}`,
            payload as any
          );
          const shippingID = res?.data?.shipping?._id;
          router.push(
            // `/sharp-exchange/${path}/orders/details?id=${id}`
            `/sharp-source/${path}/orders/details?id=${id}`
            // `/sharp-exchange/seller/orders/details?id=${id}&addressId=${shippingID}`
          );
        } else {
          setSellerPopUp(true);
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  // const choosedAddress = async () => {
  //   try {
  //     if (!selectedAddress) {
  //       toast.error("Please choose one address");
  //       return;
  //     }
  //     const payload = {
  //       address: selectedAddress._id,
  //     };
  //     const res = await callAxios(
  //       "post",
  //       // `sharp-source/app/source-order/addShippingDetails/${id}`,
  //       `sharp-source/app/source-order/addSellerShippingDetail/${id}`,
  //       payload
  //     );
  //     if (res?.status === 201) {
  //       console.log("Shipping details added successfully!");
  //       router.push(`/sharp-source/${path}/orders/details?id=${id}`);
  //     } else {
  //       console.error("Failed to add shipping details. Please try again.");
  //     }
  //   } catch (error: any) {
  //     console.log("Error adding shipping details:", error);
  //     if (error.response?.data?.message) {
  //       console.error(error.response.data.message);
  //     } else {
  //       console.error("Error adding shipping details:", error);
  //     }
  //   }
  // };

  const handleSaveNewAddress = async (newAddress: Address) => {
    try {
      const countryFromAddress = extractCountryFromAddress(newAddress.address);
      const payload = {
        name: newAddress.name,
        address: newAddress.address,
        countryCode: newAddress.countryCode,
        email: newAddress.email,
        phone: newAddress.mobile,
        city: newAddress.city,
        state: newAddress.state,
        pincode: newAddress.pinCode,
        country: countryFromAddress,
      };

      const response = await callAxios(
        "post",
        "app/user/addAddress",
        payload as any
      );

      handleClosePopup();
      setLoading(!loading);
    } catch (error) {
      console.error("Error adding address:", error);
    }
  };

  console.log("handleSaveNewAddress--->>>", handleSaveNewAddress());

  const onEditAddress = (address: any) => {
    setEditingAddress(address);
    setIsEditPopupOpen(true);
  };
  const handleConsignmentDetailChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    if (Number(value) >= 0) {
      setConsignmentDetail((prevDetails) => ({
        ...prevDetails,
        [name]: value,
      }));
    } else {
    }
  };

  const handleConfirm = async () => {
    try {
      if (
        consignmentDetail?.breadth &&
        consignmentDetail?.height &&
        consignmentDetail?.length &&
        consignmentDetail?.quantity &&
        consignmentDetail?.weight
      ) {
        const payload = {
          // order: id,
          address: selectedAddress?._id,
          // type: type,
          consignmentDetails: {
            dimensions: `${consignmentDetail?.length}*${consignmentDetail?.breadth}*${consignmentDetail?.height}`,
            weight: consignmentDetail?.weight,
            quantity: consignmentDetail?.quantity,
          },
        };
        const res = await callAxios(
          "post",
          // "app/order/addShippingDetails",
          `sharp-source/app/source-order/addSellerShippingDetail/${id}`,
          payload as any
        );
        const shippingID = res?.data?.shipping?._id;
        setSellerPopUp(false);
        router.push(
          // `/sharp-source/${path}/orders/details?id=${id}`
          `/sharp-source/${path}/orders/details?id=${id}`
          // `/sharp-exchange/seller/orders/details?id=${id}&addressId=${shippingID}`
        );
        // choosedAddress();
      } else {
        error("Error", "Please fill all the required field");
      }
    } catch (error) {
      console.log(error);
      toast.error("Something went wrong");
    }
  };
  console.log("path", path);
  const routes = [
    {
      label: "Order Details",
      path: `/sharp-source/${path}/orders/details?id=${id}`,
    },
    {
      label: "Shipment Details",
      path: `/sharp-source/${path}/orders/address?id=${id}`,
    },
  ];

  return (
    <div style={{ padding: rightSidebarOpen ? "0 10px" : "0" }}>
      <SharpSourceNavbar routes={routes} title="" description="" />
      <div className="w-full bg-[#0C0C0E] min-h-screen flex flex-col items-center">
        <div className="w-full px-[1.5rem]">
          <div className="text-2xl font-semibold">Pick-up Location</div>
          <div>Configure Collection Addresses</div>
        </div>
        <div className="w-full flex flex-col gap-6 font-outfitOnly bg-[#0C0C0E] px-5 py-5 rounded-b-xl rounded-tr-xl">
          {/* <div className="w-full">
          <div className="text-2xl font-semibold">Shipment Details</div>
          <div>Let's review the orders so we can take the necessary actions together</div>
        </div> */}
          <div className="w-full flex flex-col items-center gap-4">
            <div className="w-full flex items-end justify-end">
              <div
                className="bg-[#26ACC9] text-lg rounded-md p-2 px-4 cursor-pointer"
                onClick={() => setIsAddPopupOpen(true)}
              >
                Add Address +
              </div>
            </div>
            <div className="w-full flex flex-col items-center gap-3">
              {addresses?.address && addresses.address.length > 0 ? (
                addresses.address
                  .filter((item: any) => item.type !== "PRIMARY")
                  .map((item: any, index: any) => (
                    <div
                      className="w-full bg-[#19191C] flex items-start gap-3 p-4 rounded-lg"
                      key={index}
                    >
                      {/* <input
                      type="radio"
                      className="mt-2 w-6 h-6 rounded-full "
                      checked={selectedAddress?._id === item._id}
                      onChange={() => handleAddressSelection(handleAddressSelection)}
                    />
                    */}

                      <div
                        onClick={() => handleAddressSelection(item)}
                        className="cursor-pointer"
                      >
                        <Image
                          src={
                            selectedAddress?._id === item._id
                              ? // ? `/assets/images/checkedIcon.svg`
                                // : `/assets/images/icon.svg`
                                `/assets/images/checked.svg`
                              : `/assets/images/blueIcon.svg`
                          }
                          width={24}
                          height={24}
                          alt="image"
                          //onClick={handleOnClick('shipping')}
                        />
                      </div>

                      <div className="w-full flex flex-col items-start text-sm font-[300] gap-2">
                        <div className="w-full text-xl font-[500] flex items-center justify-between">
                          <div>{item?.name}</div>
                          <div
                            className="flex items-center gap-2 mr-2 cursor-pointer"
                            onClick={() => onEditAddress(item)}
                          >
                            <div className="text-xs font-[400]">
                              Edit/Delete
                            </div>
                            <Image
                              src="/assets/icons/edit.svg"
                              alt="edit"
                              width={15}
                              height={15}
                            />
                          </div>
                        </div>
                        <p>{item?.address}</p>
                        <p>{`${item?.countryCode}${item?.phone}`}</p>
                        <p>{item?.email}</p>
                      </div>
                    </div>
                  ))
              ) : (
                <p>No addresses available.</p>
              )}
            </div>
            <div className="w-full flex items-center justify-center my-5">
              {addresses?.address?.length > 0 && (
                <div
                  className="bg-[#26ACC9] text-lg rounded-md p-2 px-4 cursor-pointer"
                  onClick={choosedAddress}
                >
                  Select Address
                </div>
              )}
            </div>
          </div>
          {isAddPopupOpen && (
            <AddAddress
              onSave={handleSaveNewAddress}
              onClose={handleClosePopup}
              open={isAddPopupOpen}
            />
          )}
          {isEditPopupOpen && editingAddress && (
            <EditAddress
              address={editingAddress}
              onSave={handleSaveAddress}
              onDelete={handleDeleteAddress}
              onClose={handleClosePopup}
              open={isEditPopupOpen}
            />
          )}
          {sellerPopUp && (
            <div className="fixed inset-0 z-40 flex items-center justify-center bg-[#303036] bg-opacity-50 backdrop-blur-sm font-outfitOnly">
              <div className="absolute flex flex-col justify-center items-center p-5 text-base rounded-2xl bg-[#303036] w-[700px] min-h-[400px] z-50 max-w-[794px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <img
                  onClick={() => setSellerPopUp(false)}
                  loading="lazy"
                  src="https://cdn.builder.io/api/v1/image/assets/TEMP/e7ee2682fa7a548fdf5a5c557aa7a3f16bb3b79b0e69b4b82181f82e9e3933a6?"
                  className="self-end aspect-square w-[35px] cursor-pointer"
                />
                <div className="w-full flex flex-col items-center justify-center gap-4 p-5">
                  <h2 className="w-full text-center text-2xl font-semibold">
                    Consignment Details
                  </h2>
                  <div className="w-full flex items-center justify-center gap-4">
                    <div className="flex flex-col items-start gap-2">
                      <label htmlFor="length">Length:</label>
                      <input
                        type="number"
                        name="length"
                        min={1}
                        value={consignmentDetail.length}
                        onChange={handleConsignmentDetailChange}
                        placeholder="Enter Length in inches"
                        className="p-2 rounded-md outline-none bg-[#303036] border"
                        style={{
                          border: "1px solid rgba(255, 255, 255, 0.40)",
                        }}
                      />
                    </div>
                    <div className="flex flex-col items-start gap-2">
                      <label htmlFor="breadth">Breadth:</label>
                      <input
                        type="number"
                        name="breadth"
                        value={consignmentDetail.breadth}
                        onChange={handleConsignmentDetailChange}
                        placeholder="Enter Breadth in inches"
                        className="p-2 rounded-md outline-none bg-[#303036] border"
                        style={{
                          border: "1px solid rgba(255, 255, 255, 0.40)",
                        }}
                      />
                    </div>
                  </div>
                  <div className="w-full flex items-center justify-center gap-4">
                    <div className="flex flex-col items-start gap-2">
                      <label htmlFor="height">Height:</label>
                      <input
                        type="number"
                        name="height"
                        value={consignmentDetail.height}
                        onChange={handleConsignmentDetailChange}
                        placeholder="Enter Height in inches"
                        className="p-2 rounded-md outline-none bg-[#303036] border"
                        style={{
                          border: "1px solid rgba(255, 255, 255, 0.40)",
                        }}
                      />
                    </div>
                    <div className="flex flex-col items-start gap-2">
                      <label htmlFor="weight">Weight:</label>
                      <input
                        type="number"
                        name="weight"
                        value={consignmentDetail.weight}
                        onChange={handleConsignmentDetailChange}
                        placeholder="Enter Weight in kg"
                        className="p-2 rounded-md outline-none bg-[#303036] border"
                        style={{
                          border: "1px solid rgba(255, 255, 255, 0.40)",
                        }}
                      />
                    </div>
                  </div>
                  <div className="w-[77%] flex flex-col items-start gap-2">
                    <label htmlFor="quantity">Quantity:</label>
                    <input
                      type="number"
                      name="quantity"
                      value={consignmentDetail.quantity}
                      onChange={handleConsignmentDetailChange}
                      placeholder="Enter Quantity"
                      className="p-2 w-full rounded-md outline-none bg-[#303036] border"
                      style={{
                        border: "1px solid rgba(255, 255, 255, 0.40)",
                      }}
                    />
                  </div>
                  <div className="w-full flex items-center justify-center gap-4 my-3">
                    <div
                      className="text-lg rounded-md p-2 px-4 cursor-pointer min-w-[20%] text-center"
                      onClick={() => setSellerPopUp(false)}
                      style={{
                        border: "1px solid rgba(255, 255, 255, 0.40)",
                      }}
                    >
                      Back
                    </div>
                    <div
                      className="bg-[#26ACC9]  min-w-[20%] text-lg rounded-md p-2 px-4 cursor-pointer text-center"
                      onClick={handleConfirm}
                    >
                      Confirm
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Address;

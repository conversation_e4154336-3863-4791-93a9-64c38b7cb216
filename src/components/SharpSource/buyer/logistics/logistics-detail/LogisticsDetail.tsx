"use client";
import React, { useState } from "react";
import { SharpSourceNavbar } from "@/components/shared/NavbarFactory";
import { useSelector } from "react-redux";
import Image from "next/image";
import DataTable from "@/components/shared/DataTable";
import { Box } from "@mui/system";
import CustomTable from "@/components/shared/RowColTable";
import { callAxios } from "@/utilis/axios";
import { useSearchParams } from "next/navigation";
import { useCustomToast } from "@/components/shared/CustomToast";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { LogisticShipping } from "./LogisticShipping";
import { PickUp } from "./PickUp";
// import { useSearchParams } from "next/navigation";

export const LogisticsDetail = () => {
  console.log("hehe... look at me, I am here. Inside LogisticsDetail page ");

  const [isTableOpen, setIsTableOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<"SHARPBUY" | "SELF">(
    "SHARPBUY"
  );
  const router = useRouter();
  const [isShipping, setIsShipping] = useState(false);
  const [isPickUp, setPickUp] = useState(false);
  const [isLogisticShipping, setIsLogisticShipping] = useState(false);
  const [isLogisticPickUp, setIsLogisticPickUp] = useState(false);
  const [logistsicData, setLogistsicData] = useState({});
  const [rows, setRows] = useState([]);

  const handleSelectOption = (option: "SHARPBUY" | "SELF") => {
    setSelectedOption(option);
  };

  const { success, error } = useCustomToast();
  const query = useSearchParams();
  const id = query.get("id");

  const status = logistsicData?.delivery?.status;

  const parentOrderId = query.get("parentOrderId");
  const shipmentId = query.get("shipmentId");

  const fetchLogisticData = async () => {
    try {
      const logisticDataResponse = await callAxios(
        "get",
        `sharp-source/app/logistics/getBuyerShipmentLogisticDetails/${parentOrderId}/shipment/${shipmentId}`
      );

      const data = logisticDataResponse?.data?.shipmentLogisticsDetails;
      const products =
        logisticDataResponse?.data?.shipmentLogisticsDetails?.products;

      setLogistsicData(data);
      const formattedRows = products.map((product, index) => ({
        id: index + 1,
        sno: index + 1,
        partNumber: product.rfqInventoryOffer.productNo,
        manufacturer: product.rfqInventoryOffer.manufacturer,
        quantity: product.partialQuantity,
        totalValue: product.price * product.partialQuantity,
        seller: product.rfqInventoryOffer.isSellerAuthorized
          ? "Authorised only"
          : "Any",
      }));
      setRows(formattedRows);
      console.log("data from the logistic data", data);
    } catch (error) {
      console.log("error", error);
    }
  };

  useEffect(() => {
    fetchLogisticData();
  }, []);

  const handleProceed = async () => {
    console.log("proceed button clicked!!!");

    if (selectedOption === "SHARPBUY") {
      // setIsShipping(true);
      setIsLogisticShipping(true);
    } else {
      // setPickUp(true);
      setIsLogisticPickUp(true);
    }

    console.log("----> selectedOption ---->", selectedOption);

    if (!shipmentId) {
      console.log("There in no orderId");

      return;
    }
    let description =
      selectedOption === "SHARPBUY"
        ? "Buyer has selected the 1Buy delivery method."
        : "Buyer has chosen the self-pickup delivery method.";

    let payload = {
      partialOrderId: shipmentId,
      userType: "BUYER",
      mode: selectedOption,
      description: description,
    };

    try {
      const res = await callAxios(
        "post",
        `sharp-source/app/source-order/delivery`,
        payload
      );
      success("Delivery method Added");
      // router.back();
      router.push(
        `/sharp-source/buyer/logistics/logistics-detail/${selectedOption.toLowerCase()}/${shipmentId}/${parentOrderId}`
      );
    } catch (err) {
      console.log(err);
      error("Please Try Again");
    }
  };

  const toggleTableVisibility = () => {
    setIsTableOpen(!isTableOpen);
  };
  const rightSidebarOpen = useSelector(
    (state: any) => state.sidebar.rightSidebarOpen
  );

  const columns: GridColDef<(typeof rows)[number]>[] = [
    {
      field: "sno",
      headerName: "S.No",
      flex: 0.5,
      // width: 70,
      editable: false,
      headerClassName:
        "bg-[#1A1A25] text-[#C4C4C4] font-outfitOnly text-base font-medium border border-table-stroke",
      cellClassName: "text-[#F7F7F7] border border-[#272934] text-start",
    },
    {
      field: "partNumber",
      headerName: "Part Number",
      // width: 110,
      flex: 1,
      editable: false,
      headerClassName:
        "bg-[#1A1A25] text-[#C4C4C4] font-outfitOnly text-base font-medium border border-table-stroke",
      cellClassName: "text-white border border-[#272934] text-left",
      //   valueGetter: (value, row) => `${row?.name}`,
    },
    {
      field: "manufacturer",
      headerName: "Manufacturer ",
      // width: 120,
      flex: 1,
      editable: false,
      headerClassName:
        "bg-[#1A1A25] text-[#C4C4C4] font-outfitOnly text-base font-medium border border-[#272934]",
      cellClassName: "text-white border border-[#272934] text-left",
    },

    {
      field: "quantity",
      headerName: "Quantity",
      description: "This column has a value getter and is not sortable.",
      sortable: false,
      // width: 120,
      flex: 1,
      headerClassName:
        "bg-[#1A1A25] text-[#C4C4C4] font-outfitOnly text-base font-medium border border-[#272934]",
      cellClassName: "text-white border border-[#272934] text-left",
    },
    {
      field: "totalValue",
      headerName: "Total Value",
      // width: 120,
      flex: 1,
      editable: false,
      headerClassName:
        "bg-[#1A1A25] text-[#C4C4C4] font-outfitOnly text-base font-medium border border-[#272934]",
      cellClassName: "text-white border border-[#272934] text-left",
    },
    {
      field: "seller",
      headerName: "Seller",
      // width: 120,
      flex: 1,
      editable: false,
      headerClassName:
        "bg-[#1A1A25] text-[#C4C4C4] font-outfitOnly text-base font-medium border border-[#272934]",
      cellClassName: "text-white border border-[#272934] text-left",
    },
  ];
  return (
    <div style={{ padding: rightSidebarOpen ? "0 10px" : "0" }}>
      {/* <SharpSourceNavbar routes={routes} /> */}
      <div className="relative rw-full flex flex-col gap-11 font-outfitOnly bg-[#0C0C0E] px-5 py-5 rounded-r-xl rounded-b-xl">
        <div className="w-[88%]">
          <div>
            <h1 className="text-white font-outfitOnlytext-3xl font-bold leading-normal">
              Logistics
            </h1>
            <p className="text-[#DEDEDE] font-outfitOnly text-sm font-normal leading-normal">
              Track and Manage Your Shipments
            </p>
          </div>
          {/* {shipping} */}
          <div className="mt-[40px]">
            <h1 className="text-white deliveryOption font-outfitOnly text-[20px] font-medium leading-normal">
              Choose Your Delivery Option
            </h1>

            <div className="grid gap-10  consignment p-5 mt-4 items-center rounded-md bg-zinc-900 max-w-full grid-cols-1 md:grid-cols-2">
              {/* Shipping Section */}
              {!isPickUp && (
                <div
                  className="flex flex-col items-start cursor-pointer"
                  onClick={() => handleSelectOption("SHARPBUY")}
                >
                  <div className="flex items-center gap-3">
                    <img
                      loading="lazy"
                      src={
                        selectedOption === "SHARPBUY"
                          ? "/assets/images/sharpSource/blueDot.svg"
                          : "/assets/images/sharpSource/blankDot.svg"
                      }
                      className="w-6 h-6 object-contain"
                      alt="Shipping icon"
                    />
                    <div>
                      <div className="text-base font-medium text-white">
                        1Buy Shipping & Fulfillment
                      </div>
                      <div className="text-sm font-light text-stone-300">
                        Effortless Logistics for Seamless Deliveries
                      </div>
                    </div>
                  </div>

                  <div className="grid gap-6 mt-4 grid-cols-1 pl-10">
                    <div className="">
                      <div className="text-sm font-light text-stone-300">
                        Estimated Time of Delivery
                      </div>
                      {logistsicData?.dispatchDetail?.rmAction
                        ?.deliveryTimeline ? (
                        <div className="text-base font-medium text-neutral-100">
                          {
                            logistsicData?.dispatchDetail?.rmAction
                              ?.deliveryTimeline
                          }{" "}
                          days
                        </div>
                      ) : (
                        "NA"
                      )}
                    </div>
                    <div className="">
                      <div className="text-sm font-light text-stone-300">
                        Estimated Quote
                      </div>
                      {logistsicData?.dispatchDetail?.rmAction?.quote ? (
                        <div className="text-base font-medium text-neutral-100">
                          $ {logistsicData?.dispatchDetail?.rmAction?.quote}
                        </div>
                      ) : (
                        "NA"
                      )}
                    </div>
                    {isShipping && (
                      <div className="grid grid-rows-3 ">
                        <div className="text-sm font-light text-stone-300">
                          AWB
                        </div>
                        <div className="text-base font-medium text-neutral-100">
                          <Link
                            href={logistsicData?.delivery?.trackingLink}
                            target="_blank"
                          >
                            Link: Tracking link
                          </Link>
                        </div>
                        <div className="flex mt-2">
                          <Image
                            src="/assets/images/sharpSource/document.svg"
                            width={16}
                            height={16}
                          />
                          <p className="underline text-xs font-normal">
                            <Link
                              href={
                                logistsicData?.dispatchDetail?.rmAction?.awbDoc
                              }
                              target="_blank"
                              className="underline"
                            >
                              View AWB
                            </Link>
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Pickup Section */}
              {!isShipping && (
                <div
                  className={`${
                    isPickUp ? "grid grid-rows-2" : "flex flex-col"
                  } items-start cursor-pointer`}
                  onClick={() => handleSelectOption("SELF")}
                >
                  <div
                    className={`${
                      isPickUp ? "grid " : "flex items-center gap-3"
                    } `}
                  >
                    <img
                      loading="lazy"
                      src={
                        selectedOption === "SELF"
                          ? "/assets/images/sharpSource/blueDot.svg"
                          : "/assets/images/sharpSource/blankDot.svg"
                      }
                      className="w-6 h-6 object-contain"
                      alt="Pick up icon"
                    />
                    <div>
                      <div className="text-base font-medium text-white">
                        Pick Up
                      </div>
                      <div className="text-sm font-light text-stone-300">
                        Manage Your Order Collection
                      </div>
                    </div>
                  </div>

                  <div className="grid gap-6 mt-4 grid-cols-1 pl-10">
                    <div>
                      <div className="text-sm font-light text-stone-300">
                        Warehouse Location
                      </div>
                      <div className="text-base font-medium text-neutral-100">
                        1234 Warehouse Lane, Noida, Uttar Pradesh, 201301
                      </div>
                    </div>
                    <div>
                      <div className="text-sm font-light text-stone-300">
                        You can pick up your order after
                      </div>
                      <div className="text-base font-medium text-neutral-100">
                        17 days
                      </div>
                    </div>
                  </div>

                  {/* Conditionally render the iframe when isPickup is true */}
                  {/* {isPickUp && (
                    <div className="w-full h-64 mt-4">
                      <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d14002.307503769633!2d77.3807902471701!3d28.672385386891385!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x390cf06fde355feb%3A0xe4f947b5137f0172!2sMohan%20Nagar%2C%20Ghaziabad%2C%20Uttar%20Pradesh!5e0!3m2!1sen!2sin!4v1728999464616!5m2!1sen!2sin"
                        width="450"
                        height="200"
                       
                        allowfullscreen=""
                        loading="lazy"
                        referrerpolicy="no-referrer-when-downgrade"
                      ></iframe>
                    </div>
                  )} */}
                </div>
              )}
            </div>
          </div>
          {/* {consignment shipping} */}
          <div className="mt-8  w-full product-detail">
            <p className="text-xl  font-medium">
              Consignment Information: Shipment 2
            </p>
            <div className="w-full  grid-cols-4 items-center  grid gap-12 p-5 mt-4  rounded-md bg-zinc-900">
              <div className="w-full grid grid-cols gap-3 items-center">
                <span className=" text-sm font-[300] text-[#C4C4C4]">
                  Order Id:
                </span>
                <span className=" text-sm font-[500]">8476</span>
              </div>

              <div className="w-full grid grid-cols gap-3 items-start">
                <span className="col-span-1 text-sm font-[300] text-[#C4C4C4]">
                  Date of Creation:
                </span>
                <span className="col-span-2 text-sm font-[500]">
                  {/* {formatDate(orderData?.createdAt)} */}9879587
                </span>
              </div>

              <div className="w-full grid grid-cols gap-3 items-start">
                <span className="col-span-1 text-sm font-[300] text-[#C4C4C4]">
                  Total Items:
                </span>
                <span className="col-span-2 text-sm font-[500]">4</span>
              </div>

              <div className="w-full grid grid-cols gap-3 items-start">
                <span className="col-span-1 flex text-sm font-[300] text-[#C4C4C4]">
                  Total Value:
                  <Image
                    src="/assets/icons/totalItems.svg"
                    width={8}
                    height={8}
                    alt=""
                    className="shrink-0 w-4 ml-1 aspect-square cursor-pointer"
                  />
                </span>
                <span className="col-span-2 text-sm font-[500]">
                  {/* {selectedCurrency?.symbol}{" "}
            {convertTargetPrice(orderData?.amount, selectedCurrency)} */}
                  kjg
                </span>
              </div>
            </div>
          </div>
          {/* {table} */}
          <div className="mt-8">
            <div className="flex justify-between">
              <p className="text-xl font-medium">
                Products included in this Shipment
              </p>
              <Image
                src={
                  isTableOpen
                    ? "/assets/images/sharpSource/upArrow.svg"
                    : "/dropdownarrow.svg"
                }
                width={25}
                height={25}
                alt="Toggle Table"
                onClick={toggleTableVisibility}
                className="cursor-pointer"
              />
            </div>
            {isTableOpen && (
              <div className="permissionTable mt-4 rounded-md w-full h-[65vh]">
                <Box sx={{ width: "100%" }}>
                  <CustomTable
                    columns={columns}
                    rows={rows}
                    //getRowId={(row: any) => row._id+ Math.random(0,100)}
                    // onRowClick={(params: any) => handleRowClick(params.row)}
                  />
                </Box>
              </div>
            )}
          </div>
          <div className="flex justify-end mt-5 proceed">
            <button
              className="text-sm text-black font-bold px-5 py-3 bg-[#26ACC9] rounded"
              onClick={handleProceed}
            >
              Proceed
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

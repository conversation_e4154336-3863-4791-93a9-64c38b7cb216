"use client"
import { Card, CardContent, Typography } from '@mui/material';
import PendingTasks from './pendingTask/PendingTasks';
import Image from 'next/image';
import { SharpSourceNavbar } from '../../shared/NavbarFactory';
import { useSelector } from 'react-redux';
import DashboardCards from './DashboardCards';
import { ChevronForwardOutline } from 'react-ionicons'

import { UseSelector } from 'react-redux';
import data from "../../../lang/er.json"
import Link from 'next/link';
import PendingOverTask from './PendingOverTask';

const Dashboard = () => {
  const rightSidebarOpen = useSelector((state: any) => state.sidebar.rightSidebarOpen);
  const selectedTab = useSelector((state: any) => state.tab.selectedTab);
  const language= useSelector((state:any)=>state.language.language)
  
  const getTranslation = (key, language) => {
    console.log("Language:", language);
    console.log("Available languages:", Object.keys(data));
  
    // Map the language value to the appropriate key in the data file
    const languageMap = {
      "English": "englishSellerArray",
      "Mandarin": "mandarinSellerArray"
    };
  
    const languageKey = languageMap[language] || "englishSellerArray"; // Default to English if not found
    const languageObject = data[languageKey];
  
    if (!languageObject) {
      console.error(`No translations found for language: ${language}`);
      return key; // Fallback to key if no translations are found
    }
  
    const translation = languageObject[key];
    if (!translation) {
      console.warn(`No translation found for key: ${key}`);
      return key; // Fallback to key if translation is not found
    }
  
    return translation;
  };

  return (
    <div style={{ padding: rightSidebarOpen ? '0 10px' : '0' }}>
    {/* <Navbar title="Xchange Overview" description="Real-time inventory overview, active listings, orders, and analytics ."/> */}
    <SharpSourceNavbar
        title="Source Overview"
        description="Your smart, seamless solution for sourcing electronic components"
      />

    
    <div className="flex bg-backgroundFillColor rounded-b-xl rounded-tr-xl" style={{ borderTopLeftRadius: rightSidebarOpen ? '12px' : '0' }} >
        <div className="flex-grow p-6  text-white">
          
         <DashboardCards/>

        <div className="bg-[#0C0C0E] p-0 rounded-md">
          <div className="flex flex-col justify-start items-start gap-[17px] p-0  rounded-md">
            <div className="flex justify-between items-center w-full">
              <div className="text-white text-xl font-semibold outfit">{getTranslation("pendingTasks", language)}</div>
              <div className='flex pr-2 justify-center item-center cursor-pointer'>
              <Link href="/sharp-source/buyer/pending-tasks">
                 <div className="text-stone-300 text-sm font-medium outfit">{getTranslation("seeAll", language)}</div>
              </Link>
              
              <ChevronForwardOutline
                color={'#beb1b1'} 
                height="14px"
                width="14px"
                style={{marginTop: "2px"}}
              />
              </div>
            </div>
            <PendingOverTask/>
          </div>
        </div>

        {/* <div className=" w-full h-[322px] justify-start items-center gap-[21px] inline-flex mt-4">
          <div className="w-2/5 h-[322px] px-[35px] py-6 bg-gradient-to-bl from-neutral-900 via-zinc-800 to-zinc-800 rounded-[10px] shadow backdrop-blur-[23px] flex-col justify-center items-center gap-2.5 inline-flex">
            <div className="flex-col justify-center items-center gap-1.5 flex">
              <div className="">
                <div>
                  <Image src='/assets/images/smart-animate.gif' alt="smart-animate" width={55} height={55} />
                </div>
                <div className="w-[116.24px] h-[116.24px] left-0 top-0 absolute">
                  <div className="origin-top-left rotate-[-7deg] w-[104.30px] h-[104.30px] left-0 top-[12.71px] absolute">
                    <div className="w-[73.80px] h-[73.80px] left-[21.22px] top-[8.14px] absolute" />
                    <div className="w-[73.80px] h-[73.80px] left-[40.27px] top-[-4px] absolute origin-top-left rotate-[25deg]" />
                    <div className="w-[73.80px] h-[73.80px] left-[32.03px] top-[90.23px] absolute origin-top-left rotate-[-105deg]" />
                    <div className="w-[73.80px] h-[73.80px] left-[66.28px] top-[96.95px] absolute origin-top-left rotate-[-144deg]" />
                  </div>
                  <div className="origin-top-left rotate-[-7deg] w-[104.30px] h-[104.30px] left-[-0px] top-[12.71px] absolute">
                    <div className="w-[73.80px] h-[73.80px] left-[66.28px] top-[96.95px] absolute origin-top-left rotate-[-144deg] opacity-80 mix-blend-screen blur-[30.47px] flex-col justify-start items-start inline-flex" />
                  </div>
                </div>
              </div>
              <div className="self-stretch p-2.5 flex-col justify-center items-center flex">
                <div className="text-center text-white text-[40px] font-semibold outfit">Hey Appa</div>
                <div className="text-center text-violet-800 text-base font-normal outfit">I’ve got news for you</div>
              </div>
            </div>
          </div>
          <div className="industry-trends-link flex-1 flex-col justify-center items-end gap-1.5 inline-flex">
          <div className="w-full h-[141px] p-5 bg-zinc-900 rounded justify-center items-center gap-2.5 flex">
              <div className="text-stone-300 text-2xl font-normal outfit">Taiwan trade bans will likely to have marginal impact on Taiwan’s economy</div>
            </div>
            <div className="w-full h-[141px] p-5 bg-zinc-900 rounded justify-center items-center gap-2.5 flex">
              <div className="text-stone-300 text-2xl font-normal outfit">Taiwan trade bans will likely to have marginal impact on Taiwan’s economy</div>
            </div>
            <div className="justify-start items-center gap-[14.99px] inline-flex">
              <div className="w-[24.69px] h-[24.69px] justify-start items-start gap-[8.82px] flex">
                <div className="w-[24.69px] h-[24.69px] rounded-full border border-violet-800">
                  <Image src='/assets/icons/left-arrow.svg' alt="left-arrow" width={55} height={55} />
                </div>
              </div>
              <div className="justify-start items-start gap-[10.58px] flex">
                <div className="w-[21.16px] h-[7.05px] bg-violet-800 rounded" />
                <div className="w-[7.05px] h-[7.05px] bg-violet-800 rounded" />
                <div className="w-[7.05px] h-[7.05px] bg-violet-800 rounded" />
                <div className="w-[7.05px] h-[7.05px] bg-violet-800 rounded" />
                <div className="w-[7.05px] h-[7.05px] bg-violet-800 rounded" />
              </div>
              <div className="justify-start items-start gap-[7.94px] flex">
                <div className="w-[24.69px] h-[24.69px] justify-start items-start gap-[8.82px] flex">
                  <div className="w-[24.69px] h-[24.69px] rounded-full border border-violet-800">
                    <Image src='/assets/icons/right-arrow.svg' alt="right-arrow" width={55} height={55} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div> */}



      </div>
    </div>
    </div>
  );
};

export default Dashboard;

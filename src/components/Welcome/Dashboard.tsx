"use client";
import React, { useEffect, useState } from "react";
import { WelcomeDashboard } from "../shared/DashboardFactory";
import { useSelector } from "react-redux";
import Image from "next/image";
import Link from "next/link";
import { callAxios } from "@/utilis/axios";
import TrendingNews from "./TrendingNews";
import NotifyModal from "../shared/NotifyPopup";
import { DataPopup } from "../shared/CustomModal";

const Dashboard = () => {
  const { userDetails } = useSelector((state: any) => state.user);
  const language = useSelector((state: any) => state.language.language);
  
  const [open, setOpen] = useState(false);
  const [popupOpen, setpopupOpen] = useState(false);
  const [modalTitle, setModalTitle] = useState("");
  const [pendingCount, setPendingCount] = useState(0);
  const [sourcePendingTaks, setSourcePendingTaks] = useState(0);

  const handleNotifyModel = () => {
    setpopupOpen(false);
    setOpen(true);
  };

  const openPopup = (index: number) => {
    const titles = {
      1: "Your secure marketplace for buying and selling pre-owned electronic components. Stay tuned!",
      2: "The Future of AI-Driven Component Procurement. Stay tuned!",
      3: "Bringing AI Powered personalised data analytics for you, stay tuned!"
    };
    setModalTitle(titles[index] || "");
    setpopupOpen(true);
  };

  const getTranslation = (key: string) => {
    // Simplified translation - in real implementation, use proper i18n
    const translations: Record<string, string> = {
      welcome: "Welcome",
      aiPlatformDescription: "Effortlessly trim surplus with our AI-powered inventory platform and Maximize profits.",
      orderComponents: "Order components with AI-driven procurement solutions.",
      partHealthDescription: "Unlocking Part Health, BOM Optimization, Alternate parts, Compliances & risk mitigation.",
      actionPending: "Action Pending",
      uploadInventory: "Upload Inventory",
      uploadRFQ: "Upload RFQ",
      uploadBOM: "Upload BOM",
      noNewInfo: "No new info"
    };
    return translations[key] || key;
  };

  const GetPendingTask = async () => {
    try {
      const response = await callAxios("get", "app/pendingTask/getTotalPendingTasksCount");
      setPendingCount(response?.data?.pendingTaskCounts || 0);
    } catch (error) {
      console.log("error", error);
    }
  };

  const sourcePendingTasks = async () => {
    try {
      const res = await callAxios("get", "sharp-source/app/source-pendingTask/getTotalPendingTasksCount");
      setSourcePendingTaks(res?.data?.totalPending || 0);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    GetPendingTask();
    sourcePendingTasks();
  }, []);

  const maxLength = 35;
  const companyName = userDetails?.company?.companyName ?? "";
  let displayName = companyName;

  if (companyName.length > maxLength) {
    displayName = companyName.substring(0, maxLength - 2);
  }

  const customContent = (
    <>
      {/* Platform Links */}
      <div className="platforms-link grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        {/* Sharp Exchange Card */}
        <div className="sharp-exchange-link w-full flex flex-col items-start border border-violet-800 rounded-md backdrop-blur-[45px] px-[1rem] justify-between">
          <Link href="/sharp-exchange" className="w-full flex items-start">
            <div className="sharp-exchange-card py-8 flex items-start">
              <Image
                src="/assets/icons/sharpe-exchange-icon.svg"
                alt="Sharp Exchange"
                width={250}
                height={140}
                className="w-[10rem] lg:w-[200px] lg:h-[100px] xl:w-[300px] xl:h-[140px] object-contain"
              />
            </div>
          </Link>
          <div className="w-full lg:text-xs xl:text-sm font-[300]">
            {getTranslation("aiPlatformDescription")}
          </div>
          <div className="w-full flex items-center justify-between my-4 lg:my-7 lg:gap-2">
            <Link href="sharp-exchange">
              <span className="lg:text-sm xl:text-md lg:font-[300] xl:font-[500] xl:leading-[18px] lg:border-b xl:border-b-2 text-[#C4C4C4] border-white">
                {getTranslation("actionPending")}: {pendingCount}
              </span>
            </Link>
            {userDetails?.access !== "VIEW" && (
              <Link
                href="sharp-exchange/seller/inventory"
                className="lg:w-[60%] xl:w-[50%] lg:text-sm xl:text-lg lg:p-0.5 xl:p-1.5 bg-[#6D2CBF] cursor-pointer text-center"
              >
                <div>{getTranslation("uploadInventory")}</div>
              </Link>
            )}
          </div>
        </div>

        {/* Sharp Source Card */}
        <Link
          href="/sharp-source/buyer"
          className="sharp-source-link w-full flex flex-col items-start rounded-md border border-cyan-500 backdrop-blur-[45px] px-[1rem] justify-between cursor-pointer"
        >
          <div className="w-full flex items-start">
            <div className="sharp-source-link py-8 flex justify-center items-center">
              <Image
                src="/assets/icons/sharpe-source-icon.svg"
                alt="Sharp Source"
                width={250}
                height={140}
                className="w-[10rem] lg:w-[200px] ml-[-20px] lg:h-[100px] xl:w-[300px] xl:h-[140px] object-contain"
              />
            </div>
          </div>
          <div className="w-full lg:text-xs xl:text-sm font-[300]">
            {getTranslation("orderComponents")}
          </div>
          <div className="w-full flex items-center justify-between my-2 lg:my-7 lg:gap-2">
            <span className="lg:text-sm xl:text-md lg:font-[300] xl:font-[500] xl:leading-[18px] lg:border-b xl:border-b-2 text-[#C4C4C4] border-white">
              {getTranslation("actionPending")}: {sourcePendingTaks}
            </span>
            {userDetails?.access !== "VIEW" && (
              <Link
                href="/sharp-source/buyer/rfq-cart"
                className="lg:w-[60%] xl:w-[50%] lg:text-xs xl:text-lg lg:p-1 xl:p-1.5 bg-[#26ACC9] cursor-pointer text-center"
              >
                {getTranslation("uploadRFQ")}
              </Link>
            )}
          </div>
        </Link>

        {/* Sharp Data Card */}
        <div className="sharp-data-link w-full flex flex-col items-start rounded-md border border-green-500 backdrop-blur-[45px] px-[1rem] justify-between cursor-pointer">
          <div className="w-full flex items-start">
            <div className="sharp-data-link pb-8 pt-14 flex justify-center items-center">
              <Link href="https://1data.1buy.ai">
                <Image
                  src="/assets/icons/sharpe-data-icon.svg"
                  alt="Sharp Data"
                  width={250}
                  height={140}
                  className="w-[10rem] lg:w-[170px] ml-2 lg:h-[80px] xl:w-[200px] xl:h-[90px] object-contain"
                />
              </Link>
            </div>
          </div>
          <div className="w-full lg:text-xs xl:text-sm font-[300]">
            {getTranslation("partHealthDescription")}
          </div>
          <div className="w-full flex items-center justify-between gap-2 lg:my-7 lg:gap-4">
            <div className="lg:text-xs xl:text-lg lg:font-[300] xl:font-[500] xl:leading-[18px] text-[#C4C4C4] border-white">
              {getTranslation("noNewInfo")}
            </div>
            {userDetails?.access !== "VIEW" && (
              <div className="lg:w-[60%] xl:w-[50%] lg:text-xs xl:text-lg lg:p-1 xl:p-1.5 bg-[#2CBF67] cursor-pointer text-center">
                {getTranslation("uploadBOM")}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Trending News */}
      <TrendingNews />

      {/* Modals */}
      {popupOpen && (
        <DataPopup
          title={modalTitle}
          setpopupOpen={setpopupOpen}
          handleNotifyModel={handleNotifyModel}
          isStayTuned={true}
        />
      )}
      {open && <NotifyModal open={open} setOpen={setOpen} />}
    </>
  );

  return (
    <WelcomeDashboard
      title={`${getTranslation("welcome")} ${displayName}!`}
      description=""
      customContent={customContent}
    />
  );
};

export default Dashboard;

import React, { useState, useEffect, useRef, ChangeEvent, use } from "react";

import { useCallback } from "react";
import * as yup from "yup";
import GoogleMaps from "./Location";
import { callAxios } from "@/utilis/axios";
import Card from "../Profile/ProfileScreen/ToolTip";

interface FormData {
  companyName: string;
  companyAddress: string;
  addressline1: string;
  city: string;
  state: string;
  otherState: string;
  pincode: string;
  cin: string;
  gst: string;
  incorporationYear: string;
  msme: string;
  pan: string;
  companyRegistrationNumber: string;
  associatedCompany: string;
}
interface FirstCompanyPageProps {
  setData: (data: any) => void;
  setFormNo: (formNo: number) => void;
  response: {
    companyName?: string;
    country?: string;
    signzyVerified?: boolean;
  };
}

interface Company {
  companyName: string;
  companyID: string;
}

const FirstCompanyPage: React.FC<FirstCompanyPageProps> = ({
  setData,
  setFormNo,
  response,
}) => {
  const country = response?.country;
  // const country = "India";
console.log("ressss",response)
  const signzyVerified = response?.signzyVerified;

  //validation
  const validationSchema = yup.object().shape({
    // companyName: yup
    //   .string()
    //   .trim()
    //   .matches(
    //     /^[a-zA-Z\s]+$/,
    //     "CompanyName must only contain letters and spaces"
    //   )
    //   .max(80, "CompanyName must be at most 50 characters")
    //   .required("Company Name is required"),
    companyName: yup
  .string()
  .trim()
  .max(80, "Company Name must be at most 80 characters")
  .required("Company Name is required"),

    companyAddress: yup
      .string()
      .trim()
      // .max(100, "Company Address must be at most 100 characters")
      .required("Company Address is required"),
    city: yup
      .string()
      .trim()
      .matches(/^[a-zA-Z\s]+$/, "City must only contain letters and spaces")
      .max(50, "City must be at most 50 characters")
      .required("City is required"),
    state: yup.string().required("State is required"),
    pincode: yup
      .string()
      .trim()

      .matches(
        /^\d+(-\d+)?$/,
        "Pincode must be numeric and may contain a single hyphen"
      ) // Allows optional hyphen
      .required("Pincode is required"),
    incorporationYear: yup
      .string()
      .trim()
      .matches(/^\d{4}$/, "Year of Incorporation must be a 4-digit year")
      .test(
        "is-past-year",
        "Year of Incorporation cannot be in the future",
        (value) => {
          const currentYear = new Date().getFullYear();
          return parseInt(value, 10) <= currentYear;
        }
      )
      .required("Year of Incorporation is required"),
    ...(country === "India"
      ? {
          // cin: yup
          //   .string()
          //   .trim()
          //   .length(21, "CIN must be exactly 21 characters long")
          //   .required("CIN is required"),
         
          cin: yup.string(), 
          gst: yup
            .string()
            .trim()
            .length(15, "GSTIN must be exactly 15 characters long")
            .required("GST/Tax No. is required"),
          msme: yup
            .string()
            .notRequired()
            .test(
              "is-valid-length",
              "MSME must be exactly 19 characters long",
              (value) => {
                return !value || value.length === 19;
              }
            ),
          pan: yup
            .string()
            .trim()
            .length(10, "PAN number must be exactly 10 digits")
            .required("PAN is required"),
          // associatedCompany: yup
          //   .string()
          //   .required("Associated Company is required"),
        }
      : {
          companyRegistrationNumber: yup
            .string()
            .trim()
            .matches(
              /^[A-Za-z0-9]+$/,
              "CRN must contain only letters and numbers"
            )
            // .length(21, "company registration number must be 21 digits")
            .required("Company Registration Number is required"),
        }),
  });

  const validateIncorporationYear = async (year: any) => {
    try {
      const schema = yup.reach(
        validationSchema,
        "incorporationYear"
      ) as yup.AnySchema;
      await schema.validate(year);
      setErrors((prevErrors) => ({ ...prevErrors, incorporationYear: "" }));
    } catch (err) {
      if (err instanceof yup.ValidationError) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          incorporationYear: err.message,
        }));
      }
    }
  };
  //formdata
  const [formData, setFormData] = useState<FormData>({
    companyName: "",
    companyAddress: "",
    addressline1: "",
    city: "",
    state: "",
    otherState: "",
    pincode: "",
    cin: "",
    gst: "",
    incorporationYear: "",
    msme: "",
    pan: "",
    companyRegistrationNumber: "",
    associatedCompany: "",
  });

  useEffect(() => {
    if (response?.companyName) {
      setFormData((prevData: FormData) => ({
        ...prevData,
        companyName: response.companyName || "",
      }));
    }
  }, [response?.companyName]);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [keyword, setKeyword] = useState<string>("");
  const [companies, setCompanies] = useState<Company[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const [selectedCompany, setSelectedCompany] = useState("");
  const inputWrapperRef = useRef<HTMLDivElement>(null);
  const [isCinVerified, setIsCinVerified] = useState<boolean | null>(null);
  const [isPanVerified, setIsPanVerified] = useState<boolean | null>(null);
  const [isGSTVerified, setIsGSTVerified] = useState<boolean | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isCinToolTip, setIsCinToolTip] = useState(false);
  const [isPanToolTip, setIsPanToolTip] = useState(false);
  const [isGSTToolTip, setIsGSTToolTip] = useState(false);
  const [isIncorporationYearVerified, setIsIncorporationYearVerified] =
    useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = async (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { id, value } = e.target;
    try {
      const schema = yup.reach(validationSchema, id) as yup.AnySchema;
      await schema.validate(value);
      setErrors((prevErrors) => ({ ...prevErrors, [id]: "" }));
    } catch (err) {
      if (err instanceof yup.ValidationError) {
        setErrors((prevErrors) => ({ ...prevErrors, [id]: err.message }));
      }
    }
    if (id === "state") {
      setFormData((prevFormData) => ({
        ...prevFormData,
        state: value,
        otherState: value === "other" ? prevFormData.otherState : "",
      }));
    } else if (id === "otherState") {
      setFormData((prevFormData) => ({
        ...prevFormData,
        otherState: value,
      }));
    } else {
      setFormData((prevFormData) => ({
        ...prevFormData,
        [id]: value,
      }));
    }
  };

  const indianStates = {
    AP: "Andhra Pradesh",
    AR: "Arunachal Pradesh",
    AS: "Assam",
    BR: "Bihar",
    CG: "Chhattisgarh",
    GA: "Goa",
    GJ: "Gujarat",
    HR: "Haryana",
    HP: "Himachal Pradesh",
    JH: "Jharkhand",
    KA: "Karnataka",
    KL: "Kerala",
    MP: "Madhya Pradesh",
    MH: "Maharashtra",
    MN: "Manipur",
    DL: "Delhi",
    ML: "Meghalaya",
    MZ: "Mizoram",
    NL: "Nagaland",
    OD: "Odisha",
    PB: "Punjab",
    RJ: "Rajasthan",
    SK: "Sikkim",
    TN: "Tamil Nadu",
    TS: "Telangana",
    TR: "Tripura",
    UP: "Uttar Pradesh",
    UK: "Uttarakhand",
    WB: "West Bengal",
    AN: "Andaman and Nicobar Islands",
    CH: "Chandigarh",
    DN: "Dadra and Nagar Haveli and Daman and Diu",
    JK: "Jammu and Kashmir",
    LA: "Ladakh",
    LD: "Lakshadweep",
    PY: "Puducherry",
  };
  const handleIndianAddress = (address: Address) => {
    console.log("Handling Indian Address");
    const { address: streetAddress, city, state, pincode } = address;

    console.log("Received address object:", address);
    console.log("State code received:", state);

    const stateCode = (state || "").toUpperCase();
    console.log("Normalized state code:", stateCode);

    const fullStateName = indianStates[stateCode] || "Other";
    console.log("Looked up full state name:", fullStateName);

    console.log("All available state codes:", Object.keys(indianStates));

    const isRecognizedState = !!indianStates[stateCode];

    setFormData((prevData) => ({
      ...prevData,
      companyAddress: streetAddress,
      city,
      state: stateCode, 
      stateFullName: fullStateName, 
      otherState: isRecognizedState ? "" : stateCode,
      pincode: pincode.toString(),
    }));

    console.log("state", formData.state);
    console.log("Final form data:", {
      companyAddress: streetAddress,
      city,
      state: stateCode,
      stateFullName: fullStateName,
      otherState: isRecognizedState ? "" : stateCode,
      pincode: pincode.toString(),
    });
  };

  const handleOtherCountryAddress = (address: Address) => {
    const { address: streetAddress, city, state, pincode } = address;

    setFormData((prevData) => ({
      ...prevData,
      companyAddress: streetAddress,
      city,
      state: state, // For other countries, we directly use the provided state
      otherState: "",
      pincode: pincode.toString(),
    }));
  };

  const handleAddressSelect = (address: Address) => {
    console.log("Address Selected:", address);

    if (country === "India") {
      handleIndianAddress(address);
    } else {
      handleOtherCountryAddress(address);
    }

    setErrors((prevErrors) => ({
      ...prevErrors,
      companyAddress: "",
      city: address.city ? "" : prevErrors.city,
      state: address.state ? "" : prevErrors.state,
      pincode: address.pincode ? "" : prevErrors.pincode,
    }));
  };

  //submit code
  const handleSubmit = async (event: React.FormEvent) => {
      event.preventDefault();
    console.log("formdata", formData);
    try {
     
      await validationSchema.validate(formData,{abortEarly: false });
     
     
      // console.log(error)
      if (signzyVerified === true) {
       
        // if (!formData.cin || formData.cin.trim().length !== 21) {
        //   setErrors((prevErrors) => ({
        //     ...prevErrors,
        //     cin: "CIN is required and must be exactly 21 characters long",
        //   }));
        //   return; // Stop form submission if CIN validation fails
        // }
        setFormNo(2);
      } else if (
        country === "India" &&
        signzyVerified === false &&
        (!isCinVerified ||!isPanVerified || !isGSTVerified)
      ) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          cin: !isCinVerified ? "CIN verification failed" : "",
          pan: !isPanVerified ? "PAN verification failed" : "",
          gst: !isGSTVerified ? "GST verification failed" : "",
        }));
        return;
      }
      
      localStorage.setItem("firstCompanyPageData", JSON.stringify(formData));
      // console.log("llff",localStorage);
      setData(formData);
      // setFormNo(2);
      setFormNo(2);
    } catch (err) {
      console.log("error",err)
      if (err instanceof yup.ValidationError) {
        const errMessages: { [key: string]: string } = {};
        err.inner.forEach((error) => {
          if (error.path) {
            errMessages[error.path] = error.message;
          }
        });
        setErrors(errMessages);
      }
    }
  };

  // to fetch data from local
  useEffect(() => {
    console.log("gjg");
    const storedData = localStorage.getItem("firstCompanyPageData");
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      setFormData((prevData) => ({
        ...prevData,
        ...parsedData,
      }));
    } else if (response?.companyName) {
      setFormData((prevData) => ({
        ...prevData,
        companyName: response.companyName || "",
      }));
    }
  }, [response?.companyName]);

  //associated company
  const fetchCompanies = async () => {
    try {
      const response = await callAxios("post", "app/auth/companyNameSearch", {
        name: keyword.trim(),
      } as any);
      if (response.data && response.data.data && response.data.data.result) {
        setCompanies(response.data.data.result);
      } else {
        setCompanies([]);
      }
    } catch (error) {
      console.error("Error fetching companies:", error);
      setCompanies([]);
    }
  };

  useEffect(() => {
    if (keyword.trim()) {
      fetchCompanies();
    } else {
      setCompanies([]);
      setIsDropdownOpen(false);
    }
  }, [keyword]);

  // console.log("comqpnu", companies);
  const handleKeywordChange = (event: ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setKeyword(newValue);
    setFormData((prevData) => ({ ...prevData, associatedCompany: newValue }));
    setIsDropdownOpen(true);
  };

  const handleCompanySelect = (company: Company) => {
    console.log("handleCompanySelect triggered:", company);
    setKeyword(company.companyName);
    setFormData((prevData) => ({
      ...prevData,
      associatedCompany: company.companyName,
    }));
    setIsDropdownOpen(false);
  };

  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (
      inputWrapperRef.current &&
      !inputWrapperRef.current.contains(event.target as Node)
    ) {
      console.log("Clicked outside, closing dropdown");
      setIsDropdownOpen(false);
    }
  }, []);

  // verify CINDATA
  const fetchCompanyData = async () => {
    console.log("companyName", formData.companyName);
    try {
      // Determine the type of registration number to fetch data for
      const registrationNumber =
        formData.cin || formData.companyRegistrationNumber;

      if (registrationNumber) {
        const response = await callAxios("post", "app/auth/cinSearch", {
          cinNo: registrationNumber,
        } as any);
        console.log(
          "response companyneme",
          response?.data?.data?.result?.companyName
        );
        if (
          response.data.message === "CIN found" &&
          response?.data?.data?.result?.companyName === formData.companyName
        ) {
          const incorporationYear =
            response.data.data.result.dateOfIncorporation.split("/")[2];
          setFormData((prevData) => ({
            ...prevData,
            incorporationYear: incorporationYear,
          }));
          // setFormData((prevData) => ({
          //   ...prevData,
          //   incorporationYear: response.data.data.result.dateOfIncorporation.split("/")[2],
          //   // Add other fields as needed
          // }));
          setIsCinVerified(true);
          // setIsToolTip(false);
          validateIncorporationYear(incorporationYear);
        } else {
          setIsCinVerified(false);
          // setIsToolTip(true);
        }
      }
    } catch (error) {
      console.error("Error fetching company data", error);
      setIsCinVerified(false);
    }
  };

  useEffect(() => {
    if (formData.cin && signzyVerified === false) {
      fetchCompanyData();
    }
  }, [
    formData.cin,
    formData.companyRegistrationNumber,
    formData?.companyName,
    signzyVerified,
  ]);
  console.log("teure", signzyVerified);

  //PanNo
  const fetchPANData = async () => {
    if (formData.pan || formData.companyName) {
      try {
        const response = await callAxios("post", "app/auth/verifyBuisnessPan", {
          name: formData.companyName,
          panNo: formData.pan,
        } as any);
        console.log("response", response?.data?.data?.result?.verified);
        if (response?.data?.data?.result?.verified === true) {
          // Update formData based on the response if needed

          setIsPanVerified(true);
        } else {
          setIsPanVerified(false);
        }
      } catch (error) {
        console.error("Error fetching PAN data", error);
        setIsPanVerified(false);
      }
    }
  };
  useEffect(() => {
    console.log("signzy", signzyVerified);
    if (formData.pan && signzyVerified === false) {
      fetchPANData();
    }
  }, [formData.pan, formData?.companyName, signzyVerified]);

  //GST
  const fetchGSTData = async () => {
    if (formData.gst) {
      try {
        const response = await callAxios("post", "app/auth/gstSearch", {
          gstNo: formData.gst,
        } as any);
        // console.log('GST',response?.data?.data?.result?.gstnDetailed?.legalNameOfBusiness)
        // console.log("namefdf", formData.companyName)
        if (
          response.data.message === "GST No found" &&
          response?.data?.data?.result?.gstnDetailed?.legalNameOfBusiness ===
            formData.companyName
        ) {
          setIsGSTVerified(true);
        } else {
          setIsGSTVerified(false);
        }
      } catch (error) {
        console.error("Error fetching GST data", error);
        setIsGSTVerified(false);
      }
    }
  };
  useEffect(() => {
    if (formData.gst && signzyVerified === false) {
      fetchGSTData();
    }
  }, [formData.gst, formData?.companyName, signzyVerified]);

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [handleClickOutside]);

  return (
    <div className="flex flex-col items-center w-full max-w-[1202px] max-md:max-w-full font-outfitOnly ">
      <section className="pt-16 w-full">
        <h2 className="text-lg pl-6 pt-6 font-medium leading-6 text-[#C4C4C4] max-md:mt-10 max-md:max-w-full">
          Company Details
        </h2>
        <p className="pl-6 text-sm font-light text-[#FFBD2E]">
          1buy.AI{" "}
          <span className="text-[#C4C4C4]">
            is designed to provide personalised intelligent recommendations,
            enter your company details to get started.
          </span>
        </p>
        <form className="flex flex-col p-6 max-md:px-5" onSubmit={handleSubmit}>
          <div className="flex gap-4 max-md:flex-wrap">
            <InputField
              label="Company Name*"
              value={formData.companyName}
              id="companyName"
              placeholder="Enter your company name"
              onChange={handleChange}
              error={errors.companyName}
            />
            <div className="flex-1">
              <label className="text-[#C4C4C4] text-sm mb-2">
                Company Address*
              </label>
              <div
                className={`rounded-md  outline-none ${
                  errors.companyaddress ? "border-red-500" : "border-gray-300"
                } `}
              >
                <GoogleMaps
                  onAddressSelect={handleAddressSelect}
                  initialAddress={formData.companyAddress}
                  initialCity={formData.city}
                  initialState={formData.state}
                  initialPincode={formData.pincode}
                  country={response?.country}
                  error={errors.companyAddress}
                />
              </div>
              {errors.companyAddress && (
                <div className="text-sm text-red-500 mt-1 border-red-500">
                  {errors.companyAddress}
                </div>
              )}
            </div>
          </div>
          <div className="flex gap-4 mt-4 max-md:flex-wrap">
            <InputField
              label="Address Line1"
              value={formData.addressline1}
              id="addressline1"
              placeholder="Enter address"
              onChange={handleChange}
              // error={errors.city}
            />
            <InputField
              label="City*"
              value={formData.city}
              id="city"
              placeholder="Enter city name"
              onChange={handleChange}
              error={errors.city}
            />
          </div>

          <div className="flex gap-4 mt-4 max-md:flex-wrap">
            <div className="flex flex-col flex-1 max-md:max-w-full">
              {response?.country === "India" && (
                <label className="text-sm text-[#C4C4C4]" htmlFor="state">
                  State*
                </label>
              )}

              <div className="relative">
                {response.country === "India" ? (
                  <select
                    id="state"
                    name="state"
                    value={formData.state}
                    onChange={handleChange}
                    className={`flex-1 px-3.5 w-full py-2 mt-1 text-base bg-[#1A1A1A] rounded-md border border-solid ${
                      formData.state ? "text-white" : "text-[#c4c4c4b3]"
                    } border-gray-300 max-md:max-w-full appearance-none outline-none`}
                    style={{
                      backgroundImage: `url(/dropdownarrow.svg)`,
                      backgroundPosition: "right 10px center",
                      backgroundRepeat: "no-repeat",
                      backgroundSize: "16px",
                      borderColor: errors.state
                        ? "red"
                        : "rgba(255, 255, 255, 0.4)",
                    }}
                  >
                    <option value="">Enter state name</option>

                    {Object.entries(indianStates).map(
                      ([stateShort, stateFullName]) => (
                        <option key={stateShort} value={stateShort}>
                          {stateFullName}
                        </option>
                      )
                    )}

                    <option value="other">{formData.otherState}</option>
                  </select>
                ) : (
                  <InputField
                    label="State*"
                    value={formData.state}
                    id="state"
                    placeholder="Enter state name"
                    onChange={handleChange}
                    error={errors.state}
                  />
                )}
              </div>
              {response?.country === "India" && (
                <>
                  {errors.state && (
                    <div className="text-sm text-red-500 mt-1 border-red-500">
                      {errors.state}
                    </div>
                  )}
                </>
              )}
            </div>
            <InputField
              label="Pincode*"
              value={formData.pincode}
              id="pincode"
              placeholder="Enter your pincode"
              onChange={handleChange}
              error={errors.pincode}
            />
          </div>
          <div className="flex gap-4 mt-4 max-md:flex-wrap w-[50%]">
            <div className="flex flex-col flex-1 max-md:max-w-[50%]">
              <label className="text-sm text-[#C4C4C4]" htmlFor="country">
                Country*
              </label>
              <div
                className="justify-center px-3.5 py-2 
                 mt-1 text-base leading-6 text-white bg-[#1A1A1A] rounded-md border border-solid  max-md:max-w-full"
                style={{
                  borderColor: "rgba(255, 255, 255, 0.4)",
                }}
              >
                {country}
              </div>
            </div>
          </div>
          <h2 className="text-lg mt-11 pt-6 font-medium leading-6 text-[#C4C4C4] max-md:mt-10 max-md:max-w-full">
            Company Documents
          </h2>
          <p className=" text-sm font-light text-[#C4C4C4]">
            Let's get official! fill out company's document details for getting
            started.
          </p>
          {country === "India" ? (
            <>
              <div className="flex gap-4 mt-6 max-md:flex-wrap">
                <div className=" flex-col flex-1 max-md:max-w-full relative w-[50%]">
                  <InputField
                    id="pan"
                    label="Company’s PAN*"
                    value={formData.pan}
                    onChange={handleChange}
                    placeholder="Enter your company's PAN No."
                    // onChange={handleChange}
                    error={errors.pan}
                  />

                  {/* {isPanVerified !== null && (
                    <img
                      src={
                        isPanVerified
                          ? "assets/images/greentick.svg"
                          : "/assets/images/exclamation.svg"
                      }
                      className="w-4 mr-[10px]"
                      style={{
                        position: "absolute",
                        top: "20px",
                        right: "16px",
                        transform: "translateY(100%)",
                      }}
                      alt="icon"
                    />
                  )} */}
                  {isPanVerified === true && (
                    <img
                      src="assets/images/greentick.svg"
                      className="w-4 mr-[10px]"
                      style={{
                        position: "absolute",
                        top: "20px",
                        right: "16px",
                        transform: "translateY(100%)",
                      }}
                    />
                  )}
                  {isPanVerified === false && (
                    <div>
                      <img
                        src="/assets/images/exclamation.svg"
                        className="w-4 mr-[10px]"
                        style={{
                          position: "absolute",
                          top: "20px",
                          right: "16px",
                          transform: "translateY(100%)",
                        }}
                        alt="exclamation"
                        onMouseEnter={() => setIsPanToolTip(true)}
                        onMouseLeave={() => setIsPanToolTip(false)}
                      />
                      {isPanToolTip && (
                        <div className="absolute right-0 top-full  z-10">
                          <Card
                            // title="Verify CIN"
                            description="The Permanent Account number is not valid. Please ensure you have entered the registered company name. For example: Sharpbuy Global Solutions Pvt Ltd."
                          />
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <div className=" flex-col flex-1 max-md:max-w-full relative w-[50%]">
                  <InputField
                    label="Company's GST/TAX No.*"
                    value={formData.gst}
                    id="gst"
                    placeholder="Enter your company's GST/TAX No."
                    onChange={handleChange}
                    error={errors.gst}
                  />
                  {isGSTVerified === true && (
                    <img
                      src="assets/images/greentick.svg"
                      className="w-4 mr-[10px]"
                      style={{
                        position: "absolute",
                        top: "20px",
                        right: "16px",
                        transform: "translateY(100%)",
                      }}
                    />
                  )}
                  {isGSTVerified === false && (
                    <div>
                      <img
                        src="/assets/images/exclamation.svg"
                        className="w-4 mr-[10px]"
                        style={{
                          position: "absolute",
                          top: "20px",
                          right: "16px",
                          transform: "translateY(100%)",
                        }}
                        alt="exclamation"
                        onMouseEnter={() => setIsGSTToolTip(true)}
                        onMouseLeave={() => setIsGSTToolTip(false)}
                      />
                      {isGSTToolTip && (
                        <div className="absolute right-0 top-full z-10">
                          <Card
                            title=""
                            description="The  Goods & Services Tax Identification Number is not valid. Please ensure you have entered the registered company name. For example: Sharpbuy Global Solutions Pvt Ltd."
                          />
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex gap-4  mt-4 max-md:flex-wrap">
                <div className=" flex-col flex-1 max-md:max-w-full relative w-[50%]">
                  <InputField
                    label="CIN (Corporation Identification Number)*"
                    value={formData.cin}
                    id="cin"
                    placeholder="Enter your CIN"
                    onChange={handleChange}
                    error={errors.cin}
                  />

                  {isCinVerified === true && (
                    <img
                      src="assets/images/greentick.svg"
                      className="w-4 mr-[10px]"
                      style={{
                        position: "absolute",
                        top: "20px",
                        right: "16px",
                        transform: "translateY(100%)",
                      }}
                    />
                  )}
                  {isCinVerified === false && (
                    <div>
                      <img
                        src="/assets/images/exclamation.svg"
                        className="w-4 mr-[10px]"
                        style={{
                          position: "absolute",
                          top: "20px",
                          right: "16px",
                          transform: "translateY(100%)",
                        }}
                        alt="exclamation"
                        onMouseEnter={() => setIsCinToolTip(true)}
                        onMouseLeave={() => setIsCinToolTip(false)}
                      />
                      {isCinToolTip && (
                        <div className="absolute right-0 top-full  z-10">
                          <Card
                            title=""
                            description="The Corporation Identification Number is not valid. Please ensure you have entered the registered company name. For example: Sharpbuy Global Solutions Pvt Ltd."
                          />
                        </div>
                      )}
                    </div>
                  )}
                </div>
                <div className=" flex-col flex-1 max-md:max-w-full relative w-[50%]">
                  <InputField
                    label="Year of Incorporation*"
                    value={formData.incorporationYear}
                    id="incorporationYear"
                    placeholder="Enter your year of Incorporation"
                    onChange={handleChange}
                    error={errors.incorporationYear}
                    // disabled={true}
                    disabled={signzyVerified === false}
                  />

                  {signzyVerified === false && isCinVerified !== null && (
                    <img
                      src={
                        isCinVerified
                          ? "assets/images/greentick.svg"
                          : "/assets/images/exclamation.svg"
                      }
                      className="w-4 mr-[10px]"
                      style={{
                        position: "absolute",
                        top: "20px",
                        right: "16px",
                        transform: "translateY(100%)",
                      }}
                      alt="icon"
                    />
                  )}
                </div>
              </div>
              <div className="flex gap-4 mt-4">
                <InputField
                  label="MSME Reg No. (If any)"
                  value={formData.msme}
                  id="msme"
                  placeholder="Enter your MSME Reg No."
                  onChange={handleChange}
                  error={errors.msme}
                />
                <div className="flex flex-col flex-1 max-md:max-w-full">
                  <label className="text-sm text-[#C4C4C4]" htmlFor="associate">
                    Do you have any parent company?
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="associate"
                      // value={keyword}
                      value={keyword || formData.associatedCompany}
                      defaultValue={keyword}
                      onChange={handleKeywordChange}
                      onClick={fetchCompanies}
                      placeholder="Search or enter"
                      ref={inputRef}
                      className={` custom-input flex-1 px-3.5 w-full py-2 mt-1 text-base bg-[#1A1A1A] rounded-md border border-solid  text-${
                        selectedCompany ? "white" : "#c4c4c4b3"
                      }  border-gray-300 max-md:max-w-full appearance-none outline-none`}
                      style={{
                        backgroundImage: `url(/dropdownarrow.svg)`,
                        backgroundPosition: "right 10px center",
                        backgroundRepeat: "no-repeat",
                        backgroundSize: "16px",
                        borderColor: "rgba(255, 255, 255, 0.4)",
                      }}
                    />
                    {isDropdownOpen && companies.length > 0 && (
                      <div className="absolute mt-1 w-full bg-black border border-gray-300 rounded-md z-10">
                        {companies.map((company) => (
                          <div
                            key={company.companyID}
                            onClick={() => handleCompanySelect(company)}
                            className="cursor-pointer px-3.5 py-2 text-white hover:bg-neutral-700"
                          >
                            {company.companyName}
                          </div>
                        ))}
                      </div>
                    )}
                    {/* {errors.associatedCompany && (
                      <div className="text-sm text-red-500 mt-1 border-red-500">
                        {errors.associatedCompany}
                      </div>
                    )} */}
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="flex gap-4 mt-4 max-md:flex-wrap">
              {/* <InputField
                label="Company Registration Number*"
                value={formData.companyRegistrationNumber}
                id="companyRegistrationNumber"
                placeholder="Enter your Company Registration Number"
                onChange={handleChange}
                error={errors.companyRegistrationNumber}
              /> */}
              <div className=" flex-col flex-1 max-md:max-w-full relative w-[50%]">
                <InputField
                  // label="CIN (Corporation Identification Number)*"
                  // value={formData.cin}
                  // id="cin"
                  // placeholder="Enter your CIN"
                  // onChange={handleChange}
                  // error={errors.cin}
                  label="Company Registration Number*"
                  value={formData.companyRegistrationNumber}
                  id="companyRegistrationNumber"
                  placeholder="Enter your Company Registration Number"
                  onChange={handleChange}
                  error={errors.companyRegistrationNumber}
                />

                {isCinVerified !== null && (
                  <img
                    src={
                      isCinVerified
                        ? "assets/images/greentick.svg"
                        : "/assets/images/exclamation.svg"
                    }
                    className="w-4 mr-[10px]"
                    style={{
                      position: "absolute",
                      top: "20px",
                      right: "16px",
                      transform: "translateY(100%)",
                    }}
                    alt="icon"
                  />
                )}
              </div>
              {/* <InputField
                label="Year of Incorporation*"
                value={formData.incorporationYear}
                id="incorporationYear"
                placeholder="Enter your year of Incorporation"
                onChange={handleChange}
                error={errors.incorporationYear}
              /> */}
              <div className=" flex-col flex-1 max-md:max-w-full relative w-[50%]">
                <InputField
                  label="Year of Incorporation*"
                  value={formData.incorporationYear}
                  id="incorporationYear"
                  placeholder="Enter your year of Incorporation"
                  onChange={handleChange}
                  error={errors.incorporationYear}
                />

                {isCinVerified !== null && (
                  <img
                    src={
                      isCinVerified
                        ? "assets/images/greentick.svg"
                        : "/assets/images/exclamation.svg"
                    }
                    className="w-4 mr-[10px]"
                    style={{
                      position: "absolute",
                      top: "20px",
                      right: "16px",
                      transform: "translateY(100%)",
                    }}
                    alt="icon"
                  />
                )}
              </div>
            </div>
          )}

          <button
            type="submit"
            className="justify-center self-end px-5 py-2.5 mt-10 text-base bg-[#FFBD2E] font-normal tracking-wide leading-5 text-black whitespace-nowrap  rounded-md shadow-sm max-md:px-5 max-md:mr-2.5"
          >
            Save & Continue
          </button>
        </form>
      </section>
    </div>
  );
};

interface InputFieldProps {
  label: string;
  value: string;
  id: string;
  placeholder: string;
  onChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => void;
  error?: string;
  disabled?: boolean;
}

const InputField: React.FC<InputFieldProps> = ({
  label,
  value,
  id,
  placeholder,
  onChange,
  error,
  disabled,
}) => {
  return (
    <div className="flex flex-col flex-1 max-md:max-w-full">
      <label className="text-sm text-[#C4C4C4]" htmlFor={id}>
        {label}
      </label>
      <input
        id={id}
        type="text"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        className={` custom-input  px-3.5 w-full py-2 mt-1 text-base bg-[#1A1A1A] white rounded-md border border-solid ${
          error ? "border-red-500" : "border-gray-300"
        } text-white max-md:max-w-full outline-none`}
        style={{
          borderColor: error ? "#FF0000" : "rgba(255, 255, 255, 0.4)",
        }}
      />
      {error && <div className="text-sm text-red-500 mt-1">{error}</div>}
    </div>
  );
};

export default FirstCompanyPage;

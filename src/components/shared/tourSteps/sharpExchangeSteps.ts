import { Step } from "react-joyride";

export const getSharpExchangeSteps = (pathName: string): Step[] => {
  const welcomeSteps: Step[] = [
    {
      target: ".sharp-exchange-link",
      disableBeacon: true,
      content: "Secure marketplace for buying and selling electronic components.",
      title: "1 Xchange",
    },
    {
      target: ".sharp-source-link",
      content: "The Future of AI-Driven Component Procurement.",
      title: "1 Source",
    },
    {
      target: ".sharp-data-link",
      content: "AI-powered analytics for smarter data-driven decisions.",
      title: "1 Data",
    },
    {
      target: ".manage-profile-link",
      content: "Manage your account details here.",
      title: "SharpE",
    },
    {
      target: ".manage-support-link",
      content: "Need help? Our support team is just a tap away!",
      title: "SharpE",
    },
  ];

  const sharpExchangeSellerSteps: Step[] = [
    {
      target: ".toggle-profiles-link",
      content: "Need to switch gears? No sweat! Toggle between buyer and seller profiles with a tap.",
      disableBeacon: true,
      title: "SharpE",
      placement: "left",
    },
    {
      target: "._Inventory",
      content: "Click the Inventory menu button to easily Upload or view published surplus inventory.",
      title: "SharpE",
      placement: "left",
    },
    {
      target: "._Bids",
      content: "View All incoming bids and offers on you listings , negotiate directly with buyers via secure chat , and accept the best deals.",
      title: "SharpE",
      placement: "left",
    },
    {
      target: "._Switch_to",
      content: "seamlessly navigate between your products with just one click.",
      title: "SharpE",
      placement: "right",
    },
  ];

  const sharpExchangeBuyerSteps: Step[] = [
    {
      target: ".toggle-profiles-link",
      content: "Need to switch gears? No sweat! Toggle between buyer and seller profiles with a tap.",
      disableBeacon: true,
      title: "SharpE",
      placement: "left",
    },
    {
      target: "._Product_List",
      content: "Browse through our extensive catalog of electronic components.",
      title: "SharpE",
      placement: "left",
    },
    {
      target: "._Bids",
      content: "Track your bids and manage your offers here.",
      title: "SharpE",
      placement: "left",
    },
    {
      target: "._Orders",
      content: "View and manage all your orders in one place.",
      title: "SharpE",
      placement: "left",
    },
  ];

  const inventorySteps: Step[] = [
    {
      target: ".addMore",
      content: "Ready to list more components? This button lets you upload items to your inventory effortlessly.",
      disableBeacon: true,
      title: "SharpE",
      placement: "left",
    },
    {
      target: ".reviewUnPublished",
      content: "Have some listings you haven't published yet? This is where you can review and finalize them before they go live.",
      title: "SharpE",
      placement: "left",
    },
    {
      target: ".PublishedInventory",
      content: "View all your active listings and update them as needed.",
      title: "SharpE",
      placement: "",
    },
  ];

  // Helper function to check if element is available
  const addAvailabilityCheck = (steps: Step[]): Step[] => {
    return steps.map(step => ({
      ...step,
      isAvailable: () => {
        if (typeof step.target === 'string') {
          return document.querySelector(step.target) !== null;
        }
        return true;
      }
    }));
  };

  // Route-based step selection
  if (pathName === "/welcome") {
    return addAvailabilityCheck(welcomeSteps);
  } else if (pathName.includes("/sharp-exchange/seller")) {
    if (pathName.includes("/inventory")) {
      return addAvailabilityCheck(inventorySteps);
    }
    return addAvailabilityCheck(sharpExchangeSellerSteps);
  } else if (pathName.includes("/sharp-exchange/buyer")) {
    return addAvailabilityCheck(sharpExchangeBuyerSteps);
  } else if (pathName === "/sharp-exchange") {
    return addAvailabilityCheck(sharpExchangeSellerSteps);
  }

  return addAvailabilityCheck(welcomeSteps);
};

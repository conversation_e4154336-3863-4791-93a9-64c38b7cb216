import { Step } from "react-joyride";

export const getSharpSourceSteps = (pathName: string): Step[] => {
  const sharpSourceBuyerWelcome: Step[] = [
    {
      target: ".uploadRFQ",
      disableBeacon: true,
      content: "Ready to find components? Upload your RFQ list here.",
      title: "1Source",
    },
    {
      target: ".RFQ_Cart",
      content: "Manage and submit your component(MPN) list here to convert them to RFQ's.",
      title: "1Source",
      placement: "down",
    },
    {
      target: ".RFQ_List",
      content: "Review the offer received against submitted RFQ's and convert them to orders.",
      title: "1Source",
      placement: "left",
    },
    {
      target: ".pending-task",
      content: "Stay on top of your to-do list with the Pending Task Summary.",
      title: "1Source",
    },
  ];

  const buyerPendingTaskSteps: Step[] = [
    {
      target: ".pending-task-item",
      disableBeacon: true,
      content: "Here you can see all your pending tasks and their status.",
      title: "1Source",
    },
  ];

  const buyerPriceTrendSteps: Step[] = [
    {
      target: ".price-trend-chart",
      disableBeacon: true,
      content: "Explore comprehensive product details and price trends here.",
      title: "1Source",
    },
  ];

  const createRFQSteps: Step[] = [
    {
      target: ".create-rfq",
      disableBeacon: true,
      content: "Click on 'Create RFQ' to submit your matched parts into an RFQ and start receiving quotes.",
      title: "1Source",
      placement: "down",
    },
    {
      target: ".unmatched-parts",
      disableBeacon: true,
      content: "Click on the mismatched part number to view suggestions if correction is needed or wait for Our AI Assistant SharpE to update our database.",
      title: "1Source",
      placement: "down",
    },
  ];

  const rfqListSteps: Step[] = [
    {
      target: ".rfq-list-item",
      disableBeacon: true,
      content: "View all your submitted RFQs and their current status.",
      title: "1Source",
    },
  ];

  const rfqListDetailSteps: Step[] = [
    {
      target: ".rfq-detail-info",
      disableBeacon: true,
      content: "Detailed view of your RFQ with all responses from suppliers.",
      title: "1Source",
    },
  ];

  const orderDetailSteps: Step[] = [
    {
      target: ".order-detail-info",
      disableBeacon: true,
      content: "Complete details of your order including tracking and status.",
      title: "1Source",
    },
  ];

  const sellerSteps: Step[] = [
    {
      target: ".pendingRequest",
      disableBeacon: true,
      content: "View and manage all pending requests from buyers.",
      title: "1Source",
    },
    {
      target: ".RFQ_Request",
      content: "Handle incoming RFQ requests from potential buyers.",
      title: "1Source",
    },
  ];

  const rfqRequestSteps: Step[] = [
    {
      target: ".rfq-request-item",
      disableBeacon: true,
      content: "Review and respond to RFQ requests from buyers.",
      title: "1Source",
    },
  ];

  const quotationListSteps: Step[] = [
    {
      target: ".quotation-item",
      disableBeacon: true,
      content: "Manage your quotations and track their status.",
      title: "1Source",
    },
  ];

  // Helper function to check if element is available
  const addAvailabilityCheck = (steps: Step[]): Step[] => {
    return steps.map(step => ({
      ...step,
      isAvailable: () => {
        if (typeof step.target === 'string') {
          return document.querySelector(step.target) !== null;
        }
        return true;
      }
    }));
  };

  // Route-based step selection for Sharp Source
  if (pathName === "/sharp-source") {
    return addAvailabilityCheck(sharpSourceBuyerWelcome);
  } else if (pathName.includes("/sharp-source/buyer/product-list/") && /\d+\/[a-f0-9]+$/i.test(pathName)) {
    return addAvailabilityCheck(buyerPriceTrendSteps);
  } else if (pathName.includes("/sharp-source/buyer/rfq-cart/ReviewRfqCart")) {
    return addAvailabilityCheck(createRFQSteps);
  } else if (pathName.includes("/sharp-source/buyer/rfq-list/")) {
    return addAvailabilityCheck(rfqListDetailSteps);
  } else if (pathName.includes("/sharp-source/buyer/orders/")) {
    return addAvailabilityCheck(orderDetailSteps);
  } else if (pathName.includes("/sharp-source/buyer/pending-tasks")) {
    return addAvailabilityCheck(buyerPendingTaskSteps);
  } else if (pathName === "/sharp-source/buyer/rfq-list") {
    return addAvailabilityCheck(rfqListSteps);
  } else if (pathName.includes("/sharp-source/seller/pending-tasks")) {
    return addAvailabilityCheck(sellerSteps);
  } else if (pathName.includes("/sharp-source/seller/rfq-request")) {
    return addAvailabilityCheck(rfqRequestSteps);
  } else if (pathName.includes("/sharp-source/seller/quotation")) {
    return addAvailabilityCheck(quotationListSteps);
  } else if (pathName.includes("/sharp-source/seller/orders/")) {
    return addAvailabilityCheck(orderDetailSteps);
  }

  return addAvailabilityCheck(sharpSourceBuyerWelcome);
};

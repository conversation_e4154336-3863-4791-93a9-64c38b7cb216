import { Step } from "react-joyride";

export const getWelcomeSteps = (pathName: string): Step[] => {
  const welcomeSteps: Step[] = [
    {
      target: ".sharp-exchange-link",
      disableBeacon: true,
      content: "Secure marketplace for buying and selling electronic components.",
      title: "1 Xchange",
    },
    {
      target: ".sharp-source-link",
      content: "The Future of AI-Driven Component Procurement.",
      title: "1 Source",
    },
    {
      target: ".sharp-data-link",
      content: "AI-powered analytics for smarter data-driven decisions.",
      title: "1 Data",
    },
    {
      target: ".manage-profile-link",
      content: "Manage your account details here.",
      title: "SharpE",
    },
    {
      target: ".manage-support-link",
      content: "Need help? Our support team is just a tap away!",
      title: "SharpE",
    },
  ];

  // Helper function to check if element is available
  const addAvailabilityCheck = (steps: Step[]): Step[] => {
    return steps.map(step => ({
      ...step,
      isAvailable: () => {
        if (typeof step.target === 'string') {
          return document.querySelector(step.target) !== null;
        }
        return true;
      }
    }));
  };

  return addAvailabilityCheck(welcomeSteps);
};

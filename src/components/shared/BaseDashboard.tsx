"use client";
import React from "react";
import { useSelector } from "react-redux";
import { Card, CardContent, Typography } from "@mui/material";
import Image from "next/image";
import Link from "next/link";
import { ChevronForwardOutline } from "react-ionicons";

// Import unified navbar
import { SharpExchangeNavbar, SharpSourceNavbar, WelcomeNavbar } from "./NavbarFactory";

interface DashboardProps {
  variant: 'sharp-exchange' | 'sharp-source' | 'welcome';
  title: string;
  description: string;
  children?: React.ReactNode;
  showPendingTasks?: boolean;
  showDashboardCards?: boolean;
  customContent?: React.ReactNode;
}

interface DashboardCardsProps {
  access?: string;
  variant: 'sharp-exchange' | 'sharp-source';
}

interface PendingTasksProps {
  variant: 'sharp-exchange' | 'sharp-source';
}

const DashboardCards: React.FC<DashboardCardsProps> = ({ access, variant }) => {
  // This would contain the dashboard cards logic
  // For now, returning a placeholder
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
      <Card className="bg-[#1A1A1A] border-gray-700">
        <CardContent className="p-4">
          <Typography variant="h6" className="text-white mb-2">
            Total Orders
          </Typography>
          <Typography variant="h4" className="text-blue-400">
            24
          </Typography>
        </CardContent>
      </Card>
      <Card className="bg-[#1A1A1A] border-gray-700">
        <CardContent className="p-4">
          <Typography variant="h6" className="text-white mb-2">
            Active Listings
          </Typography>
          <Typography variant="h4" className="text-green-400">
            156
          </Typography>
        </CardContent>
      </Card>
      <Card className="bg-[#1A1A1A] border-gray-700">
        <CardContent className="p-4">
          <Typography variant="h6" className="text-white mb-2">
            Revenue
          </Typography>
          <Typography variant="h4" className="text-yellow-400">
            $12,450
          </Typography>
        </CardContent>
      </Card>
    </div>
  );
};

const PendingTasks: React.FC<PendingTasksProps> = ({ variant }) => {
  const language = useSelector((state: any) => state.language.language);
  
  const getTranslation = (key: string, language: string) => {
    // Simplified translation logic - in real implementation, 
    // this would use the actual translation system
    const translations: Record<string, Record<string, string>> = {
      English: {
        pendingTasks: "Pending Tasks",
        seeAll: "See All",
        pendingTasksLink: variant === 'sharp-exchange' 
          ? "/sharp-exchange/seller/pending-tasks" 
          : "/sharp-source/seller/pending-tasks"
      }
    };
    
    return translations[language]?.[key] || key;
  };

  return (
    <div className="bg-[#0C0C0E] rounded-md p-4">
      <div className="flex flex-col justify-start items-start gap-[17px] rounded-md">
        <div className="flex justify-between items-center w-full">
          <div className="text-white text-lg xl:text-xl font-semibold outfit">
            {getTranslation("pendingTasks", language)}
          </div>
          <div className="flex pr-2 justify-center item-center cursor-pointer">
            <Link href={getTranslation("pendingTasksLink", language)}>
              <div className="text-stone-300 text-sm font-medium outfit">
                {getTranslation("seeAll", language)}
              </div>
            </Link>
            <ChevronForwardOutline
              color={"#beb1b1"}
              height="14px"
              width="14px"
              style={{ marginTop: "2px" }}
            />
          </div>
        </div>
        {/* Pending tasks content would go here */}
        <div className="w-full">
          <div className="text-gray-400 text-sm">
            No pending tasks at the moment.
          </div>
        </div>
      </div>
    </div>
  );
};

const BaseDashboard: React.FC<DashboardProps> = ({
  variant,
  title,
  description,
  children,
  showPendingTasks = true,
  showDashboardCards = true,
  customContent,
}) => {
  const { leftSidebarOpen } = useSelector((state: any) => state.sidebar);
  const selectedTab = useSelector((state: any) => state.tab.selectedTab);
  const user = useSelector((state: any) => state.user);

  const renderNavbar = () => {
    const navbarProps = { title, description };
    
    switch (variant) {
      case 'sharp-exchange':
        return <SharpExchangeNavbar {...navbarProps} />;
      case 'sharp-source':
        return <SharpSourceNavbar {...navbarProps} />;
      case 'welcome':
        return <WelcomeNavbar {...navbarProps} />;
      default:
        return <SharpExchangeNavbar {...navbarProps} />;
    }
  };

  return (
    <div style={{ padding: leftSidebarOpen ? "0px 10px 10px 0px" : "10px" }}>
      {renderNavbar()}

      <div
        className={`flex bg-backgroundFillColor rounded-xl ${
          leftSidebarOpen ? "rounded-tl-none" : ""
        }`}
      >
        <div className="flex-grow p-6 text-white">
          {/* Dashboard Cards */}
          {showDashboardCards && selectedTab === "seller" && (
            <DashboardCards 
              access={user?.userDetails?.access} 
              variant={variant as 'sharp-exchange' | 'sharp-source'}
            />
          )}

          {/* Custom Content */}
          {customContent}

          {/* Default Children Content */}
          {children}

          {/* Pending Tasks */}
          {showPendingTasks && (
            <PendingTasks variant={variant as 'sharp-exchange' | 'sharp-source'} />
          )}
        </div>
      </div>
    </div>
  );
};

export default BaseDashboard;

"use client";
import React from "react";
import BaseDashboard from "./BaseDashboard";

interface DashboardFactoryProps {
  variant: 'sharp-exchange' | 'sharp-source' | 'welcome';
  title: string;
  description: string;
  children?: React.ReactNode;
  customContent?: React.ReactNode;
}

/**
 * Factory component that creates the appropriate Dashboard based on the variant
 */
const DashboardFactory: React.FC<DashboardFactoryProps> = ({
  variant,
  title,
  description,
  children,
  customContent,
}) => {
  const getConfigForVariant = (variant: string) => {
    switch (variant) {
      case 'sharp-exchange':
        return {
          showPendingTasks: true,
          showDashboardCards: true,
        };
      case 'sharp-source':
        return {
          showPendingTasks: true,
          showDashboardCards: true,
        };
      case 'welcome':
        return {
          showPendingTasks: false,
          showDashboardCards: false,
        };
      default:
        return {
          showPendingTasks: true,
          showDashboardCards: true,
        };
    }
  };

  const config = getConfigForVariant(variant);

  return (
    <BaseDashboard
      variant={variant}
      title={title}
      description={description}
      customContent={customContent}
      {...config}
    >
      {children}
    </BaseDashboard>
  );
};

// Convenience components for each variant
export const SharpExchangeDashboard: React.FC<Omit<DashboardFactoryProps, 'variant'>> = (props) => (
  <DashboardFactory {...props} variant="sharp-exchange" />
);

export const SharpSourceDashboard: React.FC<Omit<DashboardFactoryProps, 'variant'>> = (props) => (
  <DashboardFactory {...props} variant="sharp-source" />
);

export const WelcomeDashboard: React.FC<Omit<DashboardFactoryProps, 'variant'>> = (props) => (
  <DashboardFactory {...props} variant="welcome" />
);

export default DashboardFactory;

"use client";
import React from "react";
import { Step } from "react-joyride";
import BaseNavbar from "./BaseNavbar";

// Import tour steps from separate files
import { getSharpExchangeSteps } from "./tourSteps/sharpExchangeSteps";
import { getSharpSourceSteps } from "./tourSteps/sharpSourceSteps";
import { getWelcomeSteps } from "./tourSteps/welcomeSteps";

interface NavbarFactoryProps {
  title: string;
  description?: string;
  routes?: any[];
  searchBoxOpen?: boolean;
  onlySearchIcon?: boolean;
  value?: string;
  onClose?: () => void;
  variant?: 'sharp-exchange' | 'sharp-source' | 'welcome';
}

/**
 * Factory component that creates the appropriate Navbar based on the variant
 */
const NavbarFactory: React.FC<NavbarFactoryProps> = ({
  variant = 'sharp-exchange',
  ...props
}) => {
  const getStepsForVariant = (variant: string, pathName: string): Step[] => {
    switch (variant) {
      case 'sharp-exchange':
        return getSharpExchangeSteps(pathName);
      case 'sharp-source':
        return getSharpSourceSteps(pathName);
      case 'welcome':
        return getWelcomeSteps(pathName);
      default:
        return [];
    }
  };

  const getConfigForVariant = (variant: string) => {
    switch (variant) {
      case 'sharp-exchange':
        return {
          showCurrency: true,
          showNotifications: true,
          showSearch: true,
          showTour: true,
        };
      case 'sharp-source':
        return {
          showCurrency: true,
          showNotifications: true,
          showSearch: true,
          showTour: true,
        };
      case 'welcome':
        return {
          showCurrency: true,
          showNotifications: true,
          showSearch: false, // Welcome page doesn't show search
          showTour: true,
        };
      default:
        return {
          showCurrency: true,
          showNotifications: true,
          showSearch: true,
          showTour: true,
        };
    }
  };

  const config = getConfigForVariant(variant);

  return (
    <BaseNavbar
      {...props}
      variant={variant}
      {...config}
    />
  );
};

// Convenience components for each variant
export const SharpExchangeNavbar: React.FC<Omit<NavbarFactoryProps, 'variant'>> = (props) => (
  <NavbarFactory {...props} variant="sharp-exchange" />
);

export const SharpSourceNavbar: React.FC<Omit<NavbarFactoryProps, 'variant'>> = (props) => (
  <NavbarFactory {...props} variant="sharp-source" />
);

export const WelcomeNavbar: React.FC<Omit<NavbarFactoryProps, 'variant'>> = (props) => (
  <NavbarFactory {...props} variant="welcome" />
);

export default NavbarFactory;

"use client";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { usePathname, useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import Joyride, { CallBackProps, STATUS, Step } from "react-joyride";

// Store imports
import { toggleLeftSidebar, toggleRightSidebar } from "@/store/slices/sidebarSlice";
import { skipTour, startTour, stopTour } from "@/store/slices/tourGuideSlice";
import { setLanguage } from "@/store/slices/languageSlice";
import { addNotification } from "@/store/slices/notificationslice";
import { setUserDetails } from "@/store/slices/userSlice";

// Component imports
import CustomTooltip from "./TourGuide";
import { SearchDialog } from "./SearchModal";
import AnimatedSearch from "./AnimatedSearch";
import Notifications from "../Notifications";
import { CurrencySelect } from "../SharpExchnage/CurrencySelect";

// Utility imports
import { callAxios } from "@/utilis/axios";
import usePathColor from "@/hooks/GetColor";
import { onMessageListener, requestForToken } from "@/services/firebase";

// UI Components
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface NavbarProps {
  title: string;
  description?: string;
  routes?: any[];
  searchBoxOpen?: boolean;
  onlySearchIcon?: boolean;
  value?: string;
  onClose?: () => void;
  variant?: 'sharp-exchange' | 'sharp-source' | 'welcome';
  showCurrency?: boolean;
  showNotifications?: boolean;
  showSearch?: boolean;
  showTour?: boolean;
  customSteps?: Step[];
}

interface BreadcrumbProps {
  routes?: any[];
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ routes }) => {
  if (!routes || routes.length === 0) return null;

  return (
    <nav className="flex mt-2" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        {routes.map((route, index) => (
          <li key={index} className="inline-flex items-center">
            {index > 0 && (
              <svg
                className="w-3 h-3 text-gray-400 mx-1"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 6 10"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="m1 9 4-4-4-4"
                />
              </svg>
            )}
            {route.href ? (
              <Link
                href={route.href}
                className="inline-flex items-center text-sm font-medium text-gray-300 hover:text-white"
              >
                {route.label}
              </Link>
            ) : (
              <span className="text-sm font-medium text-gray-500">
                {route.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

const BaseNavbar: React.FC<NavbarProps> = ({
  title,
  description = "",
  routes,
  searchBoxOpen,
  onlySearchIcon = false,
  value,
  onClose,
  variant = 'sharp-exchange',
  showCurrency = true,
  showNotifications = true,
  showSearch = true,
  showTour = true,
  customSteps = [],
}) => {
  const pathName = usePathname();
  const router = useRouter();
  const dispatch = useDispatch();
  const color = usePathColor();

  // State
  const [open, setOpen] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [openNotifications, setOpenNotifications] = useState(false);

  // Redux selectors
  const storedLanguage = useSelector((state: any) => state.language.language);
  const { leftSidebarOpen, rightSidebarOpen } = useSelector((state: any) => state.sidebar);
  const { run, stepIndex } = useSelector((state: any) => state.tourGuide);
  const user = useSelector((state: any) => state.user);

  // Handlers
  const handleOpen = () => setIsDialogOpen(true);
  const handleClose = () => setIsDialogOpen(false);

  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status, action, index, type } = data;

    if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      dispatch(stopTour());
    }
  };

  const fetchNotifications = async () => {
    try {
      const response = await callAxios("get", "app/user/getNotification");
      setNotifications(response?.data?.notifications || []);
    } catch (error) {
      console.error("Error fetching notifications", error);
    }
  };

  const handleIconClick = () => {
    if (
      pathName === "/profile" ||
      pathName === "/users-permission" ||
      pathName === "/support"
    ) {
      router.push("/welcome");
    }
  };

  // Effects
  useEffect(() => {
    if (showNotifications) {
      fetchNotifications();
    }
  }, [openNotifications, showNotifications]);

  // Get steps based on variant and path
  const getSteps = (): Step[] => {
    if (customSteps.length > 0) return customSteps;
    
    // Return default steps based on variant
    // This will be populated with actual steps from the original components
    return [];
  };

  return (
    <div className="flex w-full">
      {showTour && (
        <Joyride
          steps={getSteps()}
          continuous
          showSkipButton
          showProgress
          run={run}
          stepIndex={stepIndex}
          callback={handleJoyrideCallback}
          tooltipComponent={CustomTooltip}
          disableOverlayClose
          disableCloseOnEsc
          styles={{
            options: {
              zIndex: 10000,
              arrowColor: "#1D1D21",
            },
          }}
        />
      )}

      <SearchDialog
        open={isDialogOpen}
        onClose={handleClose}
        value={value}
        onCloseParent={onClose}
      />

      <div className="flex bg-[#0C0C0E] w-11/12 rounded-ss-xl rounded-se-xl px-5 py-5">
        <div className="w-[88%]">
          <h1 className="text-white outfit text-xl md:text-2xl font-medium leading-normal">
            {title}
          </h1>
          <p className="text-[#C4C4C4] outfit text-sm font-normal leading-normal">
            {description}
          </p>
          <Breadcrumb routes={routes} />
        </div>

        <div className="flex justify-between items-center flex-1 gap-3">
          {showSearch && (
            <div className="search_box">
              {onlySearchIcon ? (
                <div
                  className="global-search-link relative w-8 h-8 px-30 cursor-pointer"
                  onClick={handleOpen}
                >
                  <Image
                    src="/assets/icons/search.svg"
                    alt="Search Icon"
                    layout="fill"
                    style={{ padding: '2px' }}
                  />
                </div>
              ) : (
                <>
                  {!pathName.includes("welcome") && (
                    <AnimatedSearch 
                      isDialogOpen={isDialogOpen} 
                      onOpenSearch={handleOpen} 
                    />
                  )}
                </>
              )}
            </div>
          )}

          {showCurrency && (
            <div className="manage-currency-link">
              <CurrencySelect />
            </div>
          )}

          {showNotifications && (
            <div className="manage-notifications-link">
              <Notifications
                notifications={notifications}
                openNotifications={openNotifications}
                setOpenNotifications={setOpenNotifications}
              />
            </div>
          )}

          <div className="flex items-center gap-3">
            <div
              className="manage-profile-link relative w-8 h-8 cursor-pointer"
              onClick={handleIconClick}
            >
              <Image
                src="/assets/icons/manIcon.svg"
                alt="Profile"
                layout="fill"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BaseNavbar;

"use client";
import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  redirectTo = "/login",
  fallback = null
}) => {
  const { token } = useSelector((state: RootState) => state.user);
  const router = useRouter();

  useEffect(() => {
    if (!token) {
      router.push(redirectTo);
    }
  }, [token, router, redirectTo]);

  // Show fallback while redirecting or if no token
  if (!token) {
    return fallback;
  }

  return <>{children}</>;
};

export default ProtectedRoute;

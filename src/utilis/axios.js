import axios from "axios";
import { useDispatch, useSelector } from "react-redux";

// Set the backend URL here


export const backendUrl = "https://sharpbuy-api.applore.in/api"; //dev url
// export const backendUrl = "https://api.sharpbuy.ai/api"; //prod url


//export const backendUrl = "https://sharpbuy-api.applore.in/api";


// export const backendUrl = "https://sharpbuy-api.applore.in/api";

// export const backendUrl = "http://localhost:9000/api"; //local url ->

// export const backendUrl = "https://sharpbuy-api.applore.in/api";
// export const backendUrl = "https://api-dev.sharpbuy.ai/api";

export const nudgeUrl='https://sharpbuy-nudge-api.applore.in';
// export const nudgeUrl='https://nudge.sharpbuy.ai'; //prod url

// export const backendUrl = " https://api-dev.sharpbuy.ai/api"; //staging url

// export const backendUrl = "https://ljpkhwvx-3000.inc1.devtunnels.ms//api";   //dev

export const callAxios = async (method, route, body, customheaders = {}) => {
  const details = JSON.parse(localStorage.getItem("persist:root") || "{}");
  const user = JSON.parse(details?.user || "{}");
  const token = user?.token;
  // const token ="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************.OGg1xR4b3kH_jAyAe7h6_M4Gee1HlTODRwaY3aSPigA" ||user?.token ;

  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type":
      body instanceof FormData ? "multipart/form-data" : "application/json",
    ...customheaders,
  };

  const config = {
    headers: headers,
  };

  try {
    if (method === "get") {
      return await axios.get(`${backendUrl}/${route}`, config);
    } else if (method === "delete") {
      return await axios.delete(`${backendUrl}/${route}`, {
        ...config,
        data: body,
      });
    } else if (method === "post" || method === "put" || method === "patch") {
      return await axios[method](`${backendUrl}/${route}`, body, config);
    }
  } catch (err) {
    console.error(err);
    if (err.response && err.response.status === 401) {
      localStorage.clear("token");
      localStorage.clear("user");
      //  dispatch(clearUser());
      // window.location.href = '/login'
    }
    throw err;
  }
};

// axiosInstance.js
import axios from "axios";

const axiosStrapiInstance = axios.create({
  // baseURL: 'https://sharpbuy-strapi.applore.in/', // Replace with your Strapi API URL
  baseURL: "https://strapi.1buy.ai/", // Replace with your Strapi API URL
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add a request interceptor to include the token in every request
axiosStrapiInstance.interceptors.request.use(
  (config) => {
    const token =
      "8991c81fe26227360f4b407dd53340e959909d95c41438180ea2dc683b4af9c44a4a6b1c57b079e7c172bf577bc7e655f4c0dc55b3187d486d873a8e6393c42305de256e5547f14b5c529793414458de5af50b8e3780b28fee755345d9f5656a94c9a0a4375c44dc14296a79d6478f82f6a18f2fdd9c744bee5c8420815c2f04"; // Replace with your static token
    if (token) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export default axiosStrapiInstance;

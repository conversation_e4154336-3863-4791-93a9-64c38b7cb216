import { initializeApp  } from "firebase/app";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
import { callAxios } from "../utilis/axios";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAET3BHr7hOU2pyn1Fsym9W6B6A0Xq6v5E",
  authDomain: "sharpbuy-f2d5e.firebaseapp.com",
  projectId: "sharpbuy-f2d5e",
  storageBucket: "sharpbuy-f2d5e.appspot.com",
  messagingSenderId: "730851601803",
  appId: "1:730851601803:web:0e8f2729ec1f8ce61a938e",
  measurementId: "G-2KBR56PCY3"
};

let messaging = null;

if (typeof window !== 'undefined') {
  const app = initializeApp(firebaseConfig);

  // Initialize Firebase Cloud Messaging and get a reference to the service
   messaging = getMessaging(app);
}
// Initialize Firebase


// Retrieve user data from localStorage
const getUserDetails = () => {
  const details = JSON.parse(localStorage.getItem("persist:root") || "{}");
  return JSON.parse(details?.user || "{}")?.userDetails || {};
};



// Request permission to send notifications and get the token
export const requestForToken = async (setTokenFound, setFcmToken) => {
  try {


    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration().then(registration => {
          if (registration) {
              registration.update();  // Force service worker to update
          }
      });
    }

  
    if (typeof window !== 'undefined' && typeof navigator !== "undefined" && 'serviceWorker' in navigator) {

      const permisssion = await Notification.requestPermission();
       if(permisssion === "granted") {
        const currentToken = await getToken(messaging, { vapidKey: 'BAabbLSZzUM4d8Ol0kUMUggyfCPR4f47rdQ2OiUxMqwMfv3htbBUkXD2Y2MfMOX-_G8eC2mmZjpBoBzuQUgow5U' });
        if (currentToken) {
          console.log('Current token for client: ', currentToken);
          setTokenFound(true);
          setFcmToken(currentToken);
    
          const user = getUserDetails();
          console.log("token", currentToken, user?._id);
    
          // Send the token to your server and save it in the database
          try {
            const response = await callAxios('post', 'app/user/save-fcm-token', {
              userId: user?._id,
              fcmToken: currentToken
            });
            
            console.log('Token saved successfully:', response.data);
          } catch (error) {
            console.error('Error saving token:', error);
          }
        } else {
          console.log('No registration token available. Request permission to generate one.');
          setTokenFound(false);
          setFcmToken(null);
        }       }
      else {
        console.log("not granted")
      }
     
    }

 
  } catch (err) {
    console.log('An error occurred while retrieving token. ', err);
    setTokenFound(false);
    setFcmToken(null);
  }
};

// Handle incoming messages
export const onMessageListener = () =>
  new Promise((resolve) => {
    onMessage(messaging, (payload) => {
      console.log("messaging", messaging);
      console.log("payload", payload);

      resolve(payload);
    });
  });


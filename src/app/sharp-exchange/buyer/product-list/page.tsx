"use client"
import AllProducts from "@/components/SharpExchnage/Buyer/AllProducts";
import ProductListSidebar from "@/components/SharpExchnage/Buyer/ProductListSidebar";
import { SharpExchangeNavbar } from "@/components/shared/NavbarFactory";
import { useRouter } from 'next/navigation'
import * as React from "react";
import { useSelector } from "react-redux";

interface ProductCategory {
    label: string;
    _id: string;
    imageUrl: string;
    newProductCount: string;
}

const ProductList: React.FC = () => {

    const rightSidebarOpen = useSelector((state: any) => state.sidebar.rightSidebarOpen);
    const selectedTab = useSelector((state: any) => state.tab.selectedTab);
    const [selectedCategory, setSelectedCategory] = React.useState<ProductCategory | null>(null);
    const router = useRouter();
    const [globalsearchopen, setglobalsearchopen] = React.useState(false)

    const state = useSelector((state: any) => state?.product)
console.log("state",state)

    const handleCategorySelect = (category: ProductCategory) => {
        console.log(category)
        // router.push(`/sharp-exchange/buyer/product-list?category=${category._id}`)


        setSelectedCategory(category || state?.allId?.subCategory);
    };

    



    return (
        <div style={{ padding: rightSidebarOpen ? '0 10px' : '0' }}>
            <SharpExchangeNavbar title="" description="" SearchBoxOpen={globalsearchopen} onlySearchIcon={true}  />

            <div className="flex bg-backgroundFillColor font-outfitOnly w-[99%] rounded-b-xl rounded-tr-xl" style={{ borderTopLeftRadius: rightSidebarOpen ? '12px' : '0' }} >
                <div className="flex " >
                    <ProductListSidebar handleCategorySelect={handleCategorySelect} />
                    <AllProducts selectedCategory={selectedCategory} globalsearchopen={globalsearchopen}    setGlobalSearchOpen={setglobalsearchopen}  // Pass the setter function as a prop
 />
                </div>
            </div>
        </div>
    );
}

export default ProductList;

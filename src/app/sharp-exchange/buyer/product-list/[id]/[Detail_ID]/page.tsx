"use client";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { SharpExchangeNavbar } from "@/components/shared/NavbarFactory";
import ProductListSidebar from "@/components/SharpExchnage/Buyer/ProductListSidebar";
import ProductSummary from "@/components/SharpExchnage/Buyer/productSummary";
import { callAxios } from "@/utilis/axios";
import Productattr from "@/components/Home/Products/productattr";
import { useParams } from "next/navigation";
import DocumentComponent from "@/components/Home/Products/DocsndMedia";
import Link from "next/link";
import ProductDetailTbl from "@/components/SharpExchnage/Buyer/ProductDetailTbl";
import { DataPopup } from "@/components/shared/CustomModal";
import SimilarProducts from "@/components/Home/Products/SimilarProducts";
import { useRouter } from "next/navigation";
interface ProductCategory {
  _id: string;
  label: string;
  imageUrl: string;
  newProductCount: string;
  manufacturerProductNumber: string;
}



const ProductDetail: React.FC = () => {
  const rightSidebarOpen = useSelector(
    (state: any) => state.sidebar.rightSidebarOpen
  );
  const [popupOpen, setpopupOpen] = useState(false);
  const router = useRouter()

  const [ProductDetails, setProductDetails] = useState<any>([]);
  const [sellers, setsellers] = useState<any>([]);
  // const [sortBy, setSortBy] = useState<string>(""); // Default sorting by qty
  // const [sortOrder, setSortOrder] = useState<string>("");
  const [sort_option,setSort_Options] = useState<string>("");

  const {  Detail_ID  } = useParams();

  useEffect(() => {
    fetchData();
  }, [Detail_ID,sort_option]);

  const fetchData = async () => {
    try {
      const response = await callAxios(
        "get",
        `app/product/getProductById/${Detail_ID}?${sort_option}`
      );
      console.log("API DEtails Res:", response.data.product);
      setProductDetails(response.data.product);
      setsellers(response?.data?.sellers);
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
    }
  };
   // Sort handler
   const handleSortChange = (sort_option : string) => {
    // setSort_Options(sort_option);
    
  };

  const state = useSelector((state: any) => state?.product);
  console.log({ state });

  const packagingAttribute = ProductDetails?.productAttributes?.find(
    (attr) => attr.label === "Packaging"
  );
  const ManufactureAttribute = ProductDetails?.productAttributes?.find(
    (attr) => attr.label === "Manufacturer"
  );

  const Banner = ProductDetails?.carouselMedia?.find(
    (img: any) => img?.type === "Image"
  );
  console.log({ Banner });

  const routes = [
    {
      label: state?.SelectedCat?.label,
      path: "/sharp-exchange/buyer/product-list",
    },
    {
      label: state?.SelectedSubCat?.label,
      path: `${`/sharp-exchange/buyer/product-list/${state?.SelectedSubCat?.id}`}`,
    },
    {
      label: "Product Detail",
      path: `/sharp-exchange/buyer/product-list/${state?.SelectedSubCat?._id}/`,
    },
  ];
    

  const [id, setId] = useState(null);

  const handleRedirect = () => {
    const currentPath = window.location.pathname;
    const newPath = currentPath.replace('sharp-exchange', 'sharp-source');
    
    router.push(newPath).catch(() => {
      window.location.href = newPath;
    });
  };

  return (
    <div
      className="font-outfitOnly"
      style={{ padding: rightSidebarOpen ? "0 10px" : "0" }}
    >
      <SharpExchangeNavbar title="" description="" routes={routes} />

      <div
        className=" p-4 b bg-backgroundFillColor w-[98%]  rounded-b-xl rounded-tr-xl"
        style={{ borderTopLeftRadius: rightSidebarOpen ? "12px" : "0" }}
      >
        <div className="px-2 mt-3 h-full w-[80%]">
          <h1 className="text-white outfit text-3xl font-bold leading-normal">
            Product Details
          </h1>
          <p className="text-gray-400 outfit text-sm font-normal leading-normal">
            Explore sellers, place your bid, and secure the best deals
          </p>
          <div className="p-5 mt-3 rounded-md border border-solid  bg-zinc-900 border-white border-opacity-10">
            <div className="flex ">
              <div className="text-2xl ml-5 font-semibold text-white w-[35%]">
                Part Number
              </div>
              <div className="text-2xl font-bold leading-8">
                {ProductDetails?.manufacturerProductNumber}
              </div>
            </div>
            <div className="flex gap-5 max-md:flex-col max-md:gap-0">
              <div className="flex flex-col w-[35%] max-md:ml-0 max-md:w-full">
                <div className="flex flex-col ml-5 grow max-md:mt-10 max-md:max-w-full">
                  <div className="max-w-[400px] rounded-sm">
                    <img
                      loading="lazy"
                      alt="banner"
                      src={
                        Banner?.url ||
                        "https://media.digikey.com/photos/nophoto/pna_en.jpg"
                      }
                      className="mt-4 w-full  h-full rounded-sm"
                    />
                  </div>
                  {ProductDetails?.datasheetUrl && ProductDetails.datasheetUrl.includes('http')  && 
                  <a href={ProductDetails?.datasheetUrl} target="_blank">
                    <div className="h-[30px]   download-datasheet mt-10 cursor-pointer px-2 pt-1 pb-1.5 justify-start items-start gap-2.5 inline-flex">
                      <img
                        loading="lazy"
                        alt="banner"
                        src="https://cdn.builder.io/api/v1/image/assets/TEMP/f0c282f127389fd6a1841c50bbf738f35927ed62d5060e5edfa3746f3976b309?"
                        className="shrink-0 self-start w-6 aspect-square"
                      />
                      <div className="text-[#22a95e]  font-medium text-lg font-['Outfit'] underline leading-tight ">
                        Download Data Sheet
                      </div>
                    </div>
                  </a>}
                </div>
              </div>
              <div className="flex flex-col  ml-1 w-[60%] max-md:ml-0 max-md:w-full">
                <div className="flex flex-col grow text-sm leading-5 text-white max-md:mt-10">
                  <div className="mt-6 flex  ">
                    <span className="font-medium text-white lg:min-w-[120px] xl:min-w-[150px]  ">
                      Name: &nbsp;
                    </span>
                    <span className="font-semibold text-white text-left">
                      {ProductDetails?.description}
                    </span>
                  </div>
                  <div className="mt-4 flex ">
                    <span className="font-medium text-white lg:min-w-[120px] xl:min-w-[150px] ">
                      Description: &nbsp;
                    </span>
                    <span className="font-bold text-white text-left">
                      {ProductDetails?.detailedDescription}
                    </span>
                  </div>
                  {/* <div className="mt-4">
                                    <span className="font-medium text-white">Date code:</span>{" "}
                                    <span className="text-white">CY8CMBR3110-SX2IT</span>
                                </div> */}
                  {/* <div className="mt-4 font-bold text-white text-opacity-70">
                                        Specification :<br />
                                    </div> */}
                  {/* <div className="flex   mt-3 font-bold whitespace-nowrap leading-[143%]">
                                        <div className='min-w-[300px]'>Type</div>
                                        <div className="text-right">Description</div>
                                    </div> */}
                  <div className="flex  mt-4 leading-[143%] ">
                    <div className="lg:min-w-[120px] xl:min-w-[150px] ">Category</div>
                    <div className="text-left">
                      {ProductDetails?.subCategories
                        ?.map((sub) => sub?.label)
                        .join(", ")}
                    </div>
                  </div>

                  {ManufactureAttribute && (
                    <div className="flex  py-2 mt-2 border-opacity-10">
                      <div className="font-medium  lg:min-w-[110px] xl:min-w-[150px]">
                        Manufacturer
                      </div>
                      <div className="text-right font-outfitOnly">
                        {ManufactureAttribute?.values
                          .map((v) => v?.value)
                          .join(", ")}
                      </div>
                    </div>
                  )}
                  {packagingAttribute && (
                    <div className="flex  py-2 mt-2 border-opacity-10">
                      <div className="font-medium lg:min-w-[120px] xl:min-w-[150px]">Packaging</div>
                      <div className="font-outfitOnly">
                        {packagingAttribute?.values
                          .map((v) => v.value)
                          .join(", ")}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

{ProductDetails?.productAttributes?.length > 0  &&           <Productattr productAttributes={ProductDetails?.productAttributes} />
}
          {/* {
                        ProductDetails?.otherDocsAndMedia &&
                        <DocumentComponent otherDocsAndMedia={ProductDetails?.otherDocsAndMedia} />



                    } */}

          {ProductDetails?.otherDocsAndMedia?.dataRows?.length > 0 &&
            ProductDetails?.otherDocsAndMedia?.dataHeaders?.length > 0 && (
              <DocumentComponent
                otherDocsAndMedia={ProductDetails?.otherDocsAndMedia}
              />
            )}

            {/* <SimilarProducts /> */}

          {sellers?.length > 0 ? (
            <ProductDetailTbl
              data={sellers}
              img={Banner?.url}
              productId={ProductDetails._id}
              // sortBy={sortBy}
              // sortOrder={sortOrder}
              handleSortChange={handleSortChange}
              setSort_Options={setSort_Options}
            />
          ) : (
            <div className="flex flex-col justify-center items-start p-2.5 mt-4 font-medium rounded-md border border-solid bg-zinc-900 border-white border-opacity-10 max-md:px-5">
              <div className="text-xl capitalize text-stone-300 max-md:max-w-full">
                Hang in there! Our SharpE is Searching the market For You.
              </div>
              <div className="flex gap-1 px-3 py-2 mt-5 cursor-pointer text-base text-black bg-cyan-500 rounded-lg">
                <button
                  className="px-2 pt-1 pb-1.5"
                  onClick={handleRedirect}
                >
                  Stay Tuned
                </button>
                <img
                  loading="lazy"
                  alt="stay tuned"
                  src="https://cdn.builder.io/api/v1/image/assets/TEMP/cef88e955eea15aafee041da9c30c2e6e0040e3e50437c7b3b808cc647a1cd69?"
                  className="shrink-0 my-auto w-4 aspect-[1.06]"
                />
              </div>
            </div>
          )}

          {popupOpen && (
            <DataPopup
              title="The Future of AI-Driven Component Procurement. Stay tuned!"
              setpopupOpen={setpopupOpen}
              //   handleNotifyModel={handleNotifyModel}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
"use client";
import SortFilters from "@/components/SharpExchnage/DataSorting";
import { SharpExchangeNavbar } from "@/components/shared/NavbarFactory";
import CustomTable from "@/components/shared/CustomTable";
import { useCustomToast } from "@/components/shared/CustomToast";
import CommonFilters from "@/components/shared/DataFilters";
import PaginationComp from "@/components/shared/PaginationComp";
import {
  DeleteSelectedItems,
  FilterInventoryByCountry,
  SortInventory,
  MoveUnMatchedToMatched,
  UpdateInventoryItem,
} from "@/store/slices/inventorySlice";
import { convertTargetPrice } from "@/utilis/ConvertCurrency";
import { convertToUSD } from "@/utilis/ConvertToUSD";
import { callAxios } from "@/utilis/axios";
import { Box } from "@mui/material";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useCallback, useEffect, useRef, useState } from "react";
import ReactPaginate from "react-paginate";
import { useDispatch, useSelector } from "react-redux";

function Page() {
  const rightSidebarOpen = useSelector(
    (state: any) => state.sidebar.rightSidebarOpen
  );
  const [search, setsearch] = React.useState("");
  const [editRows, setEditRows] = useState<Record<GridRowId, boolean>>({});
  const [selectedTab, setSelectedTab] = React.useState("Matched Parts");
  const [selectedrows, setSelectedrows] = useState(new Set<number>());
  const [pageNumber, setpageNumber] = useState(1);
  const [totalPages, settotalPages] = useState(0);
  const router = useRouter();
  const [showSort, setshowSort] = useState<boolean>(false);
  const [showFilter, setShowFilter] = useState<boolean>(false);

  const [searchingStatus,setSearchingStatus] = useState(true)

  const [sortOptions, setSortOptions] = useState<
    Record<string, "asc" | "desc" | null>
  >({});
  const [prevSortedOptions, setPrevSortedOptions] = useState<
    Record<string, "asc" | "desc" | null>
  >({});
  const [filters, setFilters] = useState<any>(null);
  const [itemsPerPage, setitemsPerPage] = useState(10);
  const user = useSelector((state: any) => state.user);

  const [dateLength, setdateLength] = useState(0);

  const [rows, setrows] = useState([
    // {
    //   "country": "IN",
    //   "dateCode": "Dec-22",
    //   "id": 0,
    //   "leadTime": '13',
    //   "location": "INDIA",
    //   "partNumber": "RC0402JR-0710KL",
    //   "productDescription": "RESISTOR 10K OHM 5%",
    //   "quantity": 203300 +' '+ 'PCS',
    //   "sno": 1,
    //   "yourTP": '$'+''*****,
    //   "zipCode": 110019
    // },
    // {
    //   "country": "IN",
    //   "dateCode": "Dec-22",
    //   "id": 1,
    //   "leadTime": '10',
    //   "location": "INDIA",
    //   "partNumber": "1050QC1",
    //   "productDescription": "LAMP NEON RED 120V PC MNT",
    //   "quantity": 2000 +' '+ 'PCS',
    //   "sno": 2,
    //   "yourTP": '$'+''+ 200,
    //   "zipCode": 110019
    // }
  ]);

  interface RowData {
    id: number;
    sno: number;
    productDescription: string;
    partNumber: string;
    dateCode: string;
    quantity: string;
    yourTP: string;
    leadTime: string;
    location: string;
  }

  const columns: ColumnDef<RowData>[] = [
    {
      accessorKey: "sno",
      header: "S.No",
      cell: ({ row }) => (
        <div className="text-center">{row.getValue("sno")}</div>
      ),
    },
    {
      accessorKey: "productDescription",
      header: "Product Description",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("productDescription")}</div>
      ),
    },
    {
      accessorKey: "partNumber",
      header: "Part Number",
      cell: ({ row }) => (
        <div className=" highlightedPartNumber text-left">{row.getValue("partNumber")}</div>
      ),
    },
    {
      accessorKey: "dateCode",
      header: "Date Code",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("dateCode")}</div>
      ),
    },
    {
      accessorKey: "quantity",
      header: "Quantity",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("quantity")}</div>
      ),
    },
    {
      accessorKey: "yourTP",
      header: "Your TP",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("yourTP")}</div>
      ),
    },
    {
      accessorKey: "leadTime",
      header: "Lead Time",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("leadTime")}</div>
      ),
    },
    {
      accessorKey: "location",
      header: "Location",
      cell: ({ row }) => (
        <div className="text-left">{row.getValue("location")}</div>
      ),
    },
    // {
    //   id: "actions",
    //   cell: ({ row }) => (
    //     <Button onClick={() => handleEditClick(row.original.id)}>
    //       {editRows[row.original.id] ? 'Save' : 'Edit'}
    //     </Button>
    //   ),
    // },
    // {
    //   id: "actions",
    //   cell: ({ row }) => {
    //     const rowData = row.original
    //     return (
    //       <Button
    //         variant="ghost"
    //         className="h-8 w-8 p-0"
    //         onClick={() => handleEditClick(rowData.id)}
    //       >
    //         <svg
    //           xmlns="http://www.w3.org/2000/svg"
    //           width="16"
    //           height="17"
    //           viewBox="0 0 16 17"
    //           fill="none"
    //           className="cursor-pointer"
    //         >
    //           <path
    //             d="M9.99935 4.8774L11.9993 6.8774M8.66602 14.2107H13.9993M3.33268 11.5441L2.66602 14.2107L5.33268 13.5441L13.0567 5.82007C13.3066 5.57003 13.4471 5.23095 13.4471 4.8774C13.4471 4.52385 13.3066 4.18477 13.0567 3.93474L12.942 3.82007C12.692 3.57011 12.3529 3.42969 11.9993 3.42969C11.6458 3.42969 11.3067 3.57011 11.0567 3.82007L3.33268 11.5441Z"
    //             stroke="#FFBD2E"
    //             strokeWidth="1.5"
    //             strokeLinecap="round"
    //             strokeLinejoin="round"
    //           />
    //         </svg>
    //       </Button>
    //     )
    //   },
    // },
  ];

  const dispatch = useDispatch();
  const { success, error, warning } = useCustomToast();

  const selectedCurrency = useSelector(
    (state) => state.currency.selectedCurrency
  );

  const revertTransformedData = (transformedData) => {
    if (!transformedData) return [];

    console.log("transformedData", transformedData);

    const revertItem = (item) => {
      // Extract numeric value from yourTP by removing the currency symbol
      const numericTargetPrice =
        parseFloat(
          item.yourTP?.replace(selectedCurrency?.symbol, "").replace(/,/g, "")
        ) || 0;

      return {
        product: item.product,
        description: item.productDescription || "",
        productNo: item.partNumber || "",
        dateCode: item.dateCode || "",
        quantity: parseInt(item.quantity?.split(" ")[0]) || 0,
        unitsStock: item.quantity?.split(" ")[1] || "",
        targetPrice: convertToUSD(numericTargetPrice, selectedCurrency),
        leadTime: Number(item.leadTime?.split(" ")[0]) || "",
        location: item.location || "IN",
        country: item.country || "IN",
        pinCode: item.zipCode || "",
      };
    };

    // Check if transformedData is an array or a single object
    if (Array.isArray(transformedData)) {
      return transformedData.map(revertItem);
    } else {
      return revertItem(transformedData);
    }
  };

  const handleProcessRowUpdate = async (newRow, oldRow) => {
    const data = revertTransformedData(newRow);

    dispatch(UpdateInventoryItem({ id: newRow?._id, newData: data }));
    await UpdateRow(newRow?._id, data);
    return newRow;
  };

  function handleInventoryUpdateFromUnmatchedToMatched(data: any) {
    const idsToMove = [data?.id];
    dispatch(
      MoveUnMatchedToMatched({
        idsToMove,
        newProductNo: data?.manufacturerProductNumber,
      })
    );
  }

  const UpdateRow = async (id: any, data: any) => {
    try {
      await callAxios("put", `app/inventory/editInventoryProduct/${id}`, data);
    } catch (error) {
      console.error("Error while updating", error);
    }
  };

  const handleSearch = (e: any) => {
    setsearch(e.target.value);
  };

  const InventoryState = useSelector((state: any) => state.inventory);

  const transformData = (data: any[] , page:number) => {
    if (!data) return [];

    console.log("item.productNo", data);

    return data.flat().map((item, index) => {
      const convertedTargetPrice = convertTargetPrice(
        item.targetPrice || 0,
        selectedCurrency
      );
      return {
        _id: item._id,
        id: item.product || index,
        sno: (page - 1) * 10 + index + 1,
        productDescription: item.description || "",
        partNumber: item.productNo || "",
        dateCode: item.dateCode || "",
        quantity: (item.quantity || 0) + " " + (item.unitsStock || ""),
        yourTP:
          selectedCurrency?.symbol + " " + convertedTargetPrice.toFixed(2), // Format to 2 decimal places
        leadTime: item.leadTime || "",
        location: item.country,
        country: item.country || "IN",
        zipCode: item.pinCode || "",
        status: "NOT_FOUND",
      };
    });
  };

  useEffect(() => {
    // Determine which inventory to use based on selectedTab
    const RowData =
      selectedTab === "Matched Parts"
        ? InventoryState?.MatchedInventory
        : InventoryState?.UnMatchedInventory;
    console.log("InventoryState", InventoryState);

    // debugger;
    // Apply filtering based on country if filters are present
    let filteredData = RowData;
    if (filters?.location?.length > 0) {
      const country = filters.location; // Get country from filters
      filteredData =
        RowData?.filter((item) => country.includes(item.country)) || [];
    }


  // Ensure search is not null or undefined
  if (search && search.trim() !== "") {
    filteredData = RowData?.filter(item => 
      Object.values(item).some(value => 
        value?.toString().toLowerCase().includes(search.toLowerCase())
      )
    );
    console.log("Filtered by search", filteredData); // Debug log
  }

 

  
  const IsSearching = (data: any[]) => {
    return data.every(item => item.status === 'FOUND' || item.status === 'NOT_FOUND' );
  };


    // Transform the filtered data
    const transformedData = transformData(filteredData,pageNumber);
    console.log({ transformedData });
    console.log(selectedTab, RowData, transformedData);

    // Update total pages for pagination
    settotalPages(Math.ceil((transformedData?.length || 0) / itemsPerPage));
    setdateLength(
      selectedTab === "Matched Parts"
        ? InventoryState?.MatchedInventory?.length
        : InventoryState?.UnMatchedInventory?.length
    );
    // Reset page number if the data length is greater than or equal to 10
    if (transformedData?.length <= 10) {
      setpageNumber(1);
    }

    // Calculate the start and end indices for slicing
    const startIdx = (pageNumber - 1) * itemsPerPage;
    const endIdx = startIdx + itemsPerPage;
    // Slice the data for pagination
    setrows(transformedData?.slice(startIdx, endIdx) || []);
  }, [
    selectedTab,
    InventoryState,
    pageNumber,
    itemsPerPage,
    filters,
    search,
    selectedCurrency,
  ]);


  useEffect(() => {
    setpageNumber(1);
    setsearch("")
  }, [selectedTab]);

  // useEffect(() => {
  //     const RowData = selectedTab === "Matched Parts" ? InventoryState?.MatchedInventory : InventoryState?.UnmatchedInventory || [];

  //     const transformedData = transformData(RowData);
  //     setrows(transformedData || []);
  // }, [rows])

  // const handleSelectionChange = (selectedRows) => {
  //     console.log("Selected Rows:", selectedRows);
  //     // setSelectedrows(selectedRows)
  //     // setSelectedRows(selectedRows)
  //     // Do something with the selected rows

  //     // const idsArray = Array.from(selectedRows);
  //     // console.log({idsArray})
  //     // dispatch(DeleteSelectedItems(idsArray));
  // };

  const handleSelectionChange = useCallback((selectedRows) => {
    console.log("Selected Rows:jkhkdsjgksdjgjksdgfjkdghkjgh", selectedRows);
    setSelectedrows(selectedRows);

    // Handle delete or other actions with selected rows here
  }, []);

  const DeleteSelecteditems = () => {
    if (selectedrows?.size === 0) {
      warning("message", "Please Select Inventory");
      return;
    }
    const idsArray = Array.from(selectedrows).map((id) => id);
    console.log({ idsArray });
    console.log("deleted");
    dispatch(DeleteSelectedItems(idsArray));
    console.log("setselecteddros");

    // delete from api also 
    DeleteInventories(idsArray)
    // setSelectedrows(new Set([]));
    selectedrows.clear();
  };

  const DeleteInventories = async(idsArray: number[])=>{
    try {
      // debugger;
      const response = await callAxios(
          "delete",
          "app/inventory/deletePublishedInventory",
          {
              inventoryIds: idsArray,
              type: "ARRAY",
              deleteType: "UNPUBLISHED",
          }
      );
      console.log("delete res", response);
      if (response?.status === 200) {
          success("Success", "Inventory Deleted Successfully");
          // setSelectedrows([]);
          selectedrows.clear();
          
      } else {
          warning("message", response?.data?.message);
      }
  } catch (error) {
      console.error("Error sending data:", error);
  }
  }

  const PublishSelectedInventory = async () => {
    const idsArray = Array.from(selectedrows).map((id) => id);

    console.log({ selectedrows });
    console.log({ inventoryIds: idsArray });

    try {
      const response = await callAxios(
        "post",
        "app/inventory/publishInventory",
        { inventoryIds: idsArray }
      );
      console.log({ response });
      if (response?.status === 200) {
        success("Success", response?.data?.message);
        dispatch(DeleteSelectedItems(idsArray));
        router.push("/sharp-exchange/seller/inventory");
      }
    } catch (error: any) {
      console.error("Error sending data:", error);
    }
  };

  const handlePageClick = (selectedItem: { selected: number }) => {
    // alert(selectedItem.selected)
    setpageNumber(selectedItem.selected + 1);
  };

  const routes = [
    {
      label: "Inventory",
      path: "/sharp-exchange/seller/inventory",
    },
    {
      label: "Add your inventory",
      path: "/sharp-exchange/seller/inventory/VerifyData",
    },
  ];

  const handleFiltersUpdate = (filters: any) => {
    setFilters(filters);
    // Handle the filter data as needed
    console.log("Filters from child:", filters);
  };

  const handleSortChange = (
    newSortOptions: Record<string, "asc" | "desc" | null>
  ) => {
    setPrevSortedOptions(newSortOptions);
    // const SORT_OPTION = convertSortOptionsToString(newSortOptions);
    setSortOptions(newSortOptions);
    console.log("SORT_OPTION", newSortOptions);

    dispatch(SortInventory(newSortOptions));
  };

  return (
    <div style={{ padding: rightSidebarOpen ? "0 10px" : "0" }}>
      <SharpExchangeNavbar title="" description="" routes={routes} />
      <div className="font-outfitOnly bg-[#0C0C0E] h-[120vh] xl-custom:h-[97vh] p-5 flex flex-col md:gap-2 lg:gap-5 overflow-hidden ">
        <div className="w-[1005px] text-neutral-100 text-base lg:text-2xl font-semibold font-['Outfit']">
          Verify Data
        </div>
        <p className="mt-[-20px] text-base xl:text-lg'">
          Check out your inventories for Matched/Unmatched parts and proceed to
          publish. You are one step <br /> away from getting bids.
        </p>

        <div className="flex gap-3 justify-between text-base max-md:flex-wrap">
          <div className="flex gap-0 justify-center tracking-normal text-center max-md:flex-wrap max-w-[500px]">
            <div className="flex flex-col flex-1 justify-center py-1 font-semibold text-neutral-100">
              <button
                style={{ borderRadius: "10px 0 0 10px" }}
                className={` matchedParts flex flex-col min-w-[200px] max-w-[300px] justify-center cursor-pointer  ${
                  selectedTab === "Matched Parts"
                    ? "border-violet-800 border-b-[3px] bg-gray-900"
                    : "border"
                }`}
                onClick={() => {
                  setSelectedTab("Matched Parts");
                  setFilters({});
                  setshowSort(false);
                  setShowFilter(false);
                }}
              >
                <div className="px-3 py-2 max-md:px-5">
                  Matched Parts ({InventoryState?.MatchedInventory?.length})
                </div>
              </button>
            </div>
            <div className="flex flex-col flex-1 justify-center py-1 font-light text-stone-300">
              <button
                style={{ borderRadius: "0 10px 10px 0" }}
                className={` unmatchedParts flex flex-col min-w-[200px] max-w-[300px] justify-center  cursor-pointer  ${
                  selectedTab === "Unmatched Parts"
                    ? "border-violet-800 border-b-[3px] bg-gray-900"
                    : "border"
                }`}
                onClick={() => {
                  setSelectedTab("Unmatched Parts");
                  setFilters({});
                  setshowSort(false);
                  setShowFilter(false);
                }}
              >
                <div className="px-3 py-2.5 max-md:px-5">
                  Unmatched Parts (
                  {InventoryState?.UnMatchedInventory?.length || 0})
                </div>
              </button>
            </div>
          </div>
          {selectedTab === "Matched Parts" && selectedrows?.size > 0 ? (
            <div className="flex gap-5 justify-between self-start">
              <div
                onClick={() => DeleteSelecteditems()}
                className="px-3 py-1.5 cursor-pointer text-red-500 rounded-md border border-red-500 border-solid max-md:px-5"
              >
                Delete ({selectedrows?.size || 0})
              </div>
              <div
                onClick={() => PublishSelectedInventory()}
                className=" published px-3 py-1.5 capitalize cursor-pointer bg-violet-800 rounded-md text-neutral-100 max-md:px-5"
              >
                Publish ({selectedrows?.size || 0})
              </div>
            </div>
          ) : (
            <div className="flex gap-5 justify-between self-start">
              {/* <div onClick={() => PublishSelectedInventory()} className="px-3 py-1.5 cursor-pointer capitalize bg-violet-800 rounded-md text-neutral-100 max-md:px-5">
                                Resubmit
                            </div> */}
            </div>
          )}
        </div>
        {selectedTab === "Unmatched Parts" && InventoryState?.UnMatchedInventory?.length > 0 && !searchingStatus && (
          <div className="flex flex-wrap gap-4 items-center p-5 rounded-md bg-zinc-900">
            <img
              loading="lazy"
              src="https://sharpbuy-assets.s3.ap-south-1.amazonaws.com/sharpbuy/92ea0ab2cd5e86e670b8a7262256eb717160929403ff9a0b27c1f3142ba68083.com-optimize.gif"
              className="object-contain shrink-0 self-stretch my-auto aspect-square w-[51px]"
            />
            <div className="flex flex-col flex-1 shrink self-stretch my-auto basis-0 min-w-[240px] max-md:max-w-full">
              <div className="text-2xl font-semibold text-white bg-blend-normal">
                SharpE is fetching product details for your parts.
              </div>
              <div className="mt-2 text-base font-light text-ellipsis text-stone-300 max-md:max-w-full">
                It may take up to 1 hour. You can also click on the part numbers
                to find SharpE suggestions.
              </div>
            </div>
          </div>
        )}
        <div className="flex flex-col gap-2">
          <div className="w-full flex items-center justify-between ">
            {selectedTab === "Matched Parts" ? (
              <div className="w-1/3 h-[50px] px-4 py-2.5 rounded border border-white/opacity-40 flex justify-between items-center gap-2.5">
                <div className="w-6 h-6 flex justify-center items-center">
                  <Image
                    src="/assets/icons/search.svg"
                    alt="Search"
                    width={25}
                    height={25}
                  />
                </div>
                <input
                  type="text"
                  value={search}
                  onChange={(e) => handleSearch(e)}
                  className="w-full bg-transparent text-stone-300 text-xl font-normal outfit leading-tight focus:outline-none"
                  placeholder="Search"
                />
              </div>
            ) : (
              <div className="">
                <div className=" text-base lg:text-xl ">
                  Missing Data Found (
                  {InventoryState?.UnMatchedInventory?.length || 0} rows)
                </div>
                <div className="flex gap-1 text-xs  lg:mt-2 font-light text-white">
                  <img
                    loading="lazy"
                    src="https://cdn.builder.io/api/v1/image/assets/TEMP/40d93c9355de6224025605a40c56ca69ca17fd33409afa3637d0f66db47f6ca0?apiKey=ce08c3619de1405cb26c43af35722e5a&&apiKey=ce08c3619de1405cb26c43af35722e5a"
                    className="shrink-0 self-start w-4 aspect-square"
                  />
                  <div className="text-sm">
                    The below parts numbers were not found in our database.{" "}
                    <Link href="/support">
                                             <span className="font-medium cursor-pointer text-violet-800">
                                               Request Support
                                             </span>
                                           </Link>
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center gap-6">
              <div>
                <PaginationComp
                  limit={itemsPerPage}
                  length={totalPages}
                  setlimit={setitemsPerPage}
                />
              </div>
              <div
                onClick={() => {
                  setshowSort(true);
                  setShowFilter(false);
                }}
                className="w-[100px] flex items-center justify-center gap-2 border rounded-sm border-[rgba(71, 70, 70, 0.54)] p-2.5 cursor-pointer"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M4.5 7H19.5M4.5 12H14.5M4.5 17H8.5"
                    stroke="#C4C4C4"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <span>Sort</span>
              </div>
              <div
                onClick={() => {
                  setShowFilter(true);
                  setshowSort(false);
                }}
                className="w-[100px] flex items-center justify-center gap-2 border rounded-sm border-[rgba(71, 70, 70, 0.54)] p-2.5 cursor-pointer"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M21.25 12.0018H8.895M4.534 12.0018H2.75M4.534 12.0018C4.534 11.4237 4.76368 10.8692 5.17251 10.4604C5.58134 10.0515 6.13583 9.82184 6.714 9.82184C7.29217 9.82184 7.84666 10.0515 8.25549 10.4604C8.66432 10.8692 8.894 11.4237 8.894 12.0018C8.894 12.58 8.66432 13.1345 8.25549 13.5433C7.84666 13.9522 7.29217 14.1818 6.714 14.1818C6.13583 14.1818 5.58134 13.9522 5.17251 13.5433C4.76368 13.1345 4.534 12.58 4.534 12.0018ZM21.25 18.6088H15.502M15.502 18.6088C15.502 19.1871 15.2718 19.7423 14.8628 20.1512C14.4539 20.5601 13.8993 20.7898 13.321 20.7898C12.7428 20.7898 12.1883 20.5592 11.7795 20.1503C11.3707 19.7415 11.141 19.187 11.141 18.6088M15.502 18.6088C15.502 18.0305 15.2718 17.4764 14.8628 17.0675C14.4539 16.6586 13.8993 16.4288 13.321 16.4288C12.7428 16.4288 12.1883 16.6585 11.7795 17.0674C11.3707 17.4762 11.141 18.0307 11.141 18.6088M11.141 18.6088H2.75M21.25 5.39484H18.145M13.784 5.39484H2.75M13.784 5.39484C13.784 4.81667 14.0137 4.26218 14.4225 3.85335C14.8313 3.44452 15.3858 3.21484 15.964 3.21484C16.2503 3.21484 16.5338 3.27123 16.7983 3.38079C17.0627 3.49034 17.3031 3.65092 17.5055 3.85335C17.7079 4.05578 17.8685 4.2961 17.9781 4.56059C18.0876 4.82508 18.144 5.10856 18.144 5.39484C18.144 5.68113 18.0876 5.9646 17.9781 6.22909C17.8685 6.49358 17.7079 6.7339 17.5055 6.93634C17.3031 7.13877 17.0627 7.29935 16.7983 7.4089C16.5338 7.51846 16.2503 7.57484 15.964 7.57484C15.3858 7.57484 14.8313 7.34517 14.4225 6.93634C14.0137 6.52751 13.784 5.97302 13.784 5.39484Z"
                    stroke="#C4C4C4"
                    stroke-width="1.5"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                  />
                </svg>
                <span>Filter</span>
              </div>
            </div>
          </div>

          {showFilter && (
            <div className="absolute z-50 p-2 right-10">
              <CommonFilters
                onClose={() => setShowFilter(false)}
                onApplyFilters={handleFiltersUpdate}
                prevFilters={filters}
                type={selectedTab === "Matched Parts" ? "MATCHED" : "UNMATCHED"}
                page="verify"
              />
            </div>
          )}

          {showSort && (
            <div className="absolute z-50 p-2 right-10">
              <SortFilters
                keys={["quantity", "leadTime", "targetPrice"]}
                prevSortedOptions={prevSortedOptions}
                onSortChange={handleSortChange}
                onClose={() => setshowSort(false)}
              />
            </div>
          )}

          <div className="permissionTable mt-[]">
            <Box sx={{ width: "100%" }}>
              {rows?.length > 0 && (
                <CustomTable
                  columns={columns}
                  rows={rows}
                  multipleSelectable={false}
                  handleProcessRowUpdate={handleProcessRowUpdate}
                  editRows={editRows}
                  selectedTab={selectedTab}
                  multipleSelectable={selectedTab === "Matched Parts"}
                  Showtooltip={selectedTab !== "Matched Parts"}
                  onSelectionChange={handleSelectionChange}
                  searchValue={search}
                  ShowIcon={selectedTab !== "Matched Parts"}
                  changeTab={() => setSelectedTab("Matched Parts")}
                  editable={user?.userDetails?.access !== "VIEW"}
                  dataLength={dateLength}
                  pageNumber={pageNumber}
                  limit={itemsPerPage}
                  refetchData={handleInventoryUpdateFromUnmatchedToMatched}
                  allowEdit
                />
              )}
            </Box>
          </div>
{/* {totalPages} */}
          {(rows?.length >= 9 || pageNumber > 1) && (
            <ReactPaginate
              previousLabel={
                <div className="flex items-center gap-4 cursor-pointer">
                  <img
                    src="/assets/icons/arrow_forward.svg"
                    alt="Arrow Forward"
                    className="Get_Started_arrow transform rotate-180"
                  />
                  <p>Previous</p>
                </div>
              }
              nextLabel={
                <div className="flex items-center gap-4 cursor-pointer">
                  <p>Next</p>
                  <img
                    src="/assets/icons/arrow_forward.svg"
                    alt="Arrow Forward"
                    className="Get_Started_arrow transform-[rotate(45deg)]"
                  />
                </div>
              }
              breakLabel={"..."}
              breakClassName={"break-me"}
              pageCount={totalPages}
              marginPagesDisplayed={2}
              pageRangeDisplayed={5}
              onPageChange={handlePageClick}
              containerClassName="flex justify-around items-center px-4 py-2"
              previousLinkClassName="px-4 py-2 text-white"
              nextLinkClassName="px-4 py-2 text-white"
              pageClassName="flex items-center justify-center w-10 h-10 rounded-md cursor-pointer"
              pageLinkClassName="flex items-center justify-center w-full h-full"
              activeClassName="bg-stone-900  px-2 py-2 text-white"
              activeLinkClassName="font-bold"
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default Page;

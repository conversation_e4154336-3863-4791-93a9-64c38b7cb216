"use client";
import Sidebar from "@/components/SharpExchnage/Sidebar";
import Appa from "@/components/Sidebar/Appa";
import ProtectedRoute from "@/components/ProtectedRoute";
import { ReactNode } from "react";

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <ProtectedRoute>
      <div className="flex bg-backgroundColor ">
        <Sidebar />
        {/* <ProductListSidebar /> */}
        <div className="py-5 my-2 mr-2  overflow-auto  rounded-lg flex-1">
          {/* <Navbar/> */}
          {children}
        </div>
        <Appa />

        {/* <Footer /> */}
      </div>
     </ProtectedRoute>
  );
};

export default Layout;

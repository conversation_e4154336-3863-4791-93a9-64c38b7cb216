"use client";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { SharpSourceNavbar } from "@/components/shared/NavbarFactory";
import ProductListSidebar from "@/components/SharpExchnage/Buyer/ProductListSidebar";
import ProductSummary from "@/components/SharpExchnage/Buyer/productSummary";
import { callAxios } from "@/utilis/axios";
import Productattr from "@/components/Home/Products/productattr";
import { useParams } from "next/navigation";
import DocumentComponent from "@/components/Home/Products/DocsndMedia";
import Link from "next/link";
import ProductDetailTbl from "@/components/SharpSource/buyer/ProductDetailTbl";
import LineChartComponent from "@/components/shared/LineChartproduct";
import RFQRequest from "@/components/SharpSource/buyer/RFQ/RFQRequest";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Define TypeScript interfaces for better type safety
interface ProductAttribute {
  label: string;
  values: { value: string }[];
}

interface ProductDetails {
  _id: string;
  manufacturerProductNumber: string;
  description: string;
  detailedDescription: string;
  productAttributes: ProductAttribute[];
  subCategories: { label: string }[];
  carouselMedia: { type: string; url: string }[];
  datasheetUrl: string;
  otherDocsAndMedia: any[];
}

interface Seller {
  // Define seller properties as per API response
}

interface PriceTrendData {
  months: string[];
  weightedUnitRates: number[];
  lowestPrices: number[];
  productNumber: string;
  years: number;
}

const ProductDetail: React.FC = () => {
  const rightSidebarOpen = useSelector(
    (state: any) => state.sidebar.rightSidebarOpen
  );

  const [productDetails, setProductDetails] = useState<ProductDetails | null>(
    null
  );
  const [sellers, setSellers] = useState<Seller[]>([]);

  const [AddtoCartForm,setAddtoCartForm] = useState(false)

  const [chartData, setChartData] = useState<
    { month: string; medianRate: number }[]
  >([]);
  const [trendYearData, setTrendYearData] = useState<
    { month: string; medianRate: number }[]
  >([]);
  const { Detail_ID } = useParams();

  console.log("Detail_ID - Detail_ID", Detail_ID);

  console.log("check the trendYearData", trendYearData);

  const [year, setYear] = useState<string>("2024");

  console.log("changing the year", year);

  const language = useSelector((state: any) => state.language.language);

  useEffect(() => {
    if (Detail_ID) {
      fetchData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [Detail_ID, year]);

  console.log("chartDatachartDatachartDatachartData", chartData);

  const fetchData = async () => {
    try {
      const response = await callAxios(
        "get",
        `app/product/getProductById/${Detail_ID}`
      );
      const product: ProductDetails = response.data.product;
      setProductDetails(product);
      fetchGraphData(product.manufacturerProductNumber, year);
      setSellers(response.data.sellers);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const fetchGraphData = async (partNumber: string, year: string) => {
    try {
      const response = await callAxios(
        "get",
        `sharp-source/app/source-products/getProductPriceTrendData/${Detail_ID}?year=${year}`
        // `sharp-source/app/source-products/getProductPriceTrendData/66705b1ad87244965bbf7d1e?year=2024`
      );
      if (response.status === 200) {
        const data: PriceTrendData = response.data.data;
        const combinedData = data.months.map((month, index) => ({
          month: month.split("-")[0], // Extract abbreviated month
          medianRate: data.weightedUnitRates[index] || 0,
          lowestPrice: data.lowestPrices[index] || 0, // Add lowestPrice
        }));
        setChartData(combinedData);
      }
    } catch (error) {
      console.error("Error fetching Yearly product Analytics data:", error);
      setChartData([]);
    }
  };

  const fetchTrendYears = async () => {
    try {
      const response = await callAxios(
        "get",
        `sharp-source/app/source-products/getProductYearTrendData/${Detail_ID}`
      );
      if (response.status === 200) {
        const data = response?.data?.data;
        setTrendYearData(data);
      }
    } catch (error) {
      console.error("Error fetching Available Year of Product data:", error);
      setTrendYearData([]);
    }
  };

  useEffect(() => {
    fetchTrendYears();
  }, [Detail_ID]);

  const productAttributes = productDetails?.productAttributes || [];

  const packagingAttribute = productAttributes.find(
    (attr) => attr.label === "Packaging"
  );
  const manufacturerAttribute = productAttributes.find(
    (attr) => attr.label === "Manufacturer"
  );

  const banner = productDetails?.carouselMedia.find(
    (img) => img.type === "Image"
  );

  const state = useSelector((state: any) => state.product);

  const routes = [
    {
      label: state?.SelectedCat?.label,
      path: "/sharp-source/buyer/product-list",
    },
    {
      label: state?.SelectedSubCat?.label,
      path: `/sharp-source/buyer/product-list/${state?.SelectedSubCat?.id}`,
    },
    {
      label: "Product Details",
      path: `/sharp-source/buyer/product-list/${state?.SelectedSubCat?._id}/`,
    },
  ];

  const getTranslatedMonth = (month: string, language: string) => {
    const monthTranslations: { [key: string]: { [key: string]: string } } = {
      English: {
        Jan: "Jan",
        Feb: "Feb",
        Mar: "Mar",
        Apr: "Apr",
        May: "May",
        Jun: "Jun",
        Jul: "Jul",
        Aug: "Aug",
        Sep: "Sep",
        Oct: "Oct",
        Nov: "Nov",
        Dec: "Dec",
      },
      Mandarin: {
        Jan: "一月",
        Feb: "二月",
        Mar: "三月",
        Apr: "四月",
        May: "五月",
        Jun: "六月",
        Jul: "七月",
        Aug: "八月",
        Sep: "九月",
        Oct: "十月",
        Nov: "十一月",
        Dec: "十二月",
      },
    };

    return monthTranslations[language]?.[month] || month;
  };

  const ProductPresent_inRfq = async()=>{
 
    try {
      const response = await callAxios(
        "get",
        `sharp-source/app/source-inventory/getProductIsInRfqCartStatus/${Detail_ID}`,
       
      );

      console.log("response present",response?.data?.data?.status)
      setAddtoCartForm(response?.data?.data?.status)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(()=>{
    ProductPresent_inRfq()
  },[])





  return (
    <div
      className="font-outfitOnly"
      style={{ padding: rightSidebarOpen ? "0 10px" : "0" }}
    >
      <SharpSourceNavbar title="" description="" routes={routes} />

      <div
        className="p-4 bg-backgroundFillColor w-[98%] rounded-b-xl rounded-tr-xl"
        style={{ borderTopLeftRadius: rightSidebarOpen ? "12px" : "0" }}
      >
        <div className="px-2 mt-3 h-full w-[95%] ">
          <h1 className="text-white outfit text-3xl font-bold leading-normal">
            Product Details
          </h1>
          <p className="text-gray-400 outfit text-sm font-normal leading-normal">
            Explore Comprehensive Product Details to Make Informed Decisions
          </p>
          <div className="p-5 mt-3 rounded-md border border-solid bg-zinc-900 border-white border-opacity-10">
            <div className="grid grid-cols-3 justify-between items-center">
              <div className="text-2xl ml-5 font-semibold text-white w-full">
                Product Detail
              </div>
              <div className="text-2xl font-bold leading-8">
                {productDetails?.manufacturerProductNumber}
              </div>
              {chartData.length > 0 && (
                <div className="text-2xl ml-5 font-semibold text-white w-full">
                  Price Trend Graph
                </div>
              )}
            </div>
            <div className="grid grid-cols-3 gap-4 xl:gap-5 max-md:flex-col max-md:gap-0">
              {/* Product Image and Datasheet */}
              <div className="flex flex-col w-full max-md:ml-0 max-md:w-full">
                <div className="flex flex-col xl:ml-5 grow max-md:mt-10 max-md:max-w-full">
                  <div className="max-w-[300px] xl:max-w-[400px] rounded-sm">
                    <img
                      loading="lazy"
                      src={
                        banner?.url ||
                        "https://media.digikey.com/photos/nophoto/pna_en.jpg"
                      }
                      className="mt-4 w-full h-full rounded-sm"
                      alt="Product Banner"
                    />
                  </div>
                  <a
                    href={productDetails?.datasheetUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <div className="h-[30px] mt-10 cursor-pointer px-2 pt-1 pb-1.5 justify-start items-start gap-2.5 inline-flex">
                      <img
                        loading="lazy"
                        src="https://cdn.builder.io/api/v1/image/assets/TEMP/f0c282f127389fd6a1841c50bbf738f35927ed62d5060e5edfa3746f3976b309?"
                        className="shrink-0 self-start w-6 aspect-square"
                        alt="Download Icon"
                      />
                      <div className="text-[#22a95e] font-medium text-lg font-['Outfit'] underline leading-tight">
                        View Data Sheet
                      </div>
                    </div>
                  </a>
                </div>
              </div>

              {/* Product Information */}
              <div className="flex flex-col ml-1 w-full max-md:ml-0 max-md:w-full">
                <div className="flex flex-col grow text-sm leading-5 text-white max-md:mt-10">
                  <div className="mt-6 flex">
                    <span className="font-medium text-white min-w-[100px] xl:min-w-[130px]">
                      Name: &nbsp;
                    </span>
                    <span className="font-semibold text-white text-left">
                      {productDetails?.description}
                    </span>
                  </div>
                  <div className="mt-4 flex">
                    <span className="font-medium text-white min-w-[100px] xl:min-w-[130px]">
                      Description: &nbsp;
                    </span>
                    <span className="font-bold text-white text-left">
                      {productDetails?.detailedDescription}
                    </span>
                  </div>
                  <div className="flex mt-4 leading-[143%]">
                    <div className="min-w-[100px] xl:min-w-[130px]">
                      Category
                    </div>
                    <div className="text-left">
                      {productDetails?.subCategories
                        ?.map((sub) => sub.label)
                        .join(", ")}
                    </div>
                  </div>

                  {manufacturerAttribute && (
                    <div className="flex py-2 mt-2 border-opacity-10">
                      <div className="font-medium min-w-[130px]">
                        Manufacturer
                      </div>
                      <div className="text-right font-outfitOnly">
                        {manufacturerAttribute.values
                          .map((v) => v.value)
                          .join(", ")}
                      </div>
                    </div>
                  )}
                  {packagingAttribute && (
                    <div className="flex py-2 mt-2 border-opacity-10">
                      <div className="font-medium min-w-[130px]">Packaging</div>
                      <div className="text-right font-outfitOnly">
                        {packagingAttribute.values
                          .map((v) => v.value)
                          .join(", ")}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Price Trend Graph */}
              {chartData.length > 0 && (
                <div className="flex flex-col ml-0 xl:ml-1 w-full max-md:ml-0 max-md:w-full">
                  <div className="flex gap-2 xl:gap-0 xl:justify-between items-center">
                    <div className="flex justify-between items-center mt-5">
                      <Select value={year} onValueChange={setYear}>
                        <SelectTrigger className="w-[80px] xl:w-[120px] h-[36px] text-white p-1 rounded-md border-2">
                          <SelectValue placeholder="Select Year" />
                        </SelectTrigger>
                        {/* <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Year</SelectLabel>
                            <SelectItem value="2022">2022</SelectItem>
                            <SelectItem value="2023">2023</SelectItem>
                            <SelectItem value="2024">2024</SelectItem>
                          </SelectGroup>
                        </SelectContent> */}
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>Year</SelectLabel>
                            {trendYearData.map((year) => (
                              <SelectItem value={year.toString()}>
                                {year}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Import/Export Indicators */}
                    <div className="flex flex-row gap-2">
                      <div className="flex flex-row gap-1 xl:gap-2 mt-6">
                        <div className="flex items-center space-x-2">
                          <div className="bg-[#7EDDA9] w-2 h-2 rounded-full"></div>
                          <div className="text-white text-[10px]">
                            Weighted Rate
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="bg-[#8F90DC] w-2 h-2 rounded-full"></div>
                          <div className="text-white text-[10px]">
                            Lowest Rate
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {chartData.length > 0 ? (
                    <div className="mt-5">
                      <LineChartComponent
                        parts={chartData}
                        height={250}
                        getTranslatedMonth={(month) =>
                          getTranslatedMonth(month, language)
                        }
                      />
                    </div>
                  ) : (
                    <div className="mt-5 text-center text-white text-sm font-semibold">
                      No Trends Found
                    </div>
                  )}

                  {/* <div className="mt-5">
                    <LineChartComponent
                      parts={chartData}
                      height={250}
                      getTranslatedMonth={(month) =>
                        getTranslatedMonth(month, language)
                      }
                    />
                  </div> */}
                </div>
              )}
            </div>
          </div>
{
  !AddtoCartForm &&  <div className="requestRFQ">
  <RFQRequest productId={Detail_ID} />
</div>
}
         
          <Productattr productAttributes={productDetails?.productAttributes} />
          {productDetails?.otherDocsAndMedia && (
            <DocumentComponent
              otherDocsAndMedia={productDetails.otherDocsAndMedia}
            />
          )}

          {/* Uncomment and use if needed
          {sellers?.length > 0 ? (
            <ProductDetailTbl data={sellers} img={banner?.url} productId={productDetails._id} />
          ) : (
            <div className="flex flex-col justify-center items-start p-2.5 mt-4 font-medium rounded-md border border-solid bg-zinc-900 border-white border-opacity-10 max-md:px-5">
              <div className="text-xl capitalize text-stone-300 max-md:max-w-full">
                Hang in there! Our SharpE is Searching the market For You.
              </div>
              <div className="flex gap-1 px-3 py-2 mt-5 cursor-pointer text-base text-black bg-cyan-500 rounded-lg">
                <div className="px-2 pt-1 pb-1.5">Go to Sharp Source</div>
                <img
                  loading="lazy"
                  src="https://cdn.builder.io/api/v1/image/assets/TEMP/cef88e955eea15aafee041da9c30c2e6e0040e3e50437c7b3b808cc647a1cd69?"
                  className="shrink-0 my-auto w-4 aspect-[1.06]"
                  alt="Sharp Source Icon"
                />
              </div>
            </div>
          )}
          */}
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;

"use client"
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { SharpSourceNavbar } from "@/components/shared/NavbarFactory";
import ProductListSidebar from "@/components/SharpExchnage/Buyer/ProductListSidebar";
import ProductSummary from "@/components/SharpSource/buyer/productSummary";
import ProductSubCategorySidebar from '@/components/SharpSource/buyer/ProductSubcategoryLeftsidebar';
import { setSelectedSubCat } from '@/store/slices/productSlice';
import { useParams } from 'next/navigation';

interface ProductCategory {
  _id: string;
  label: string;
  imageUrl: string;
  newProductCount: string;
}

const ProductLisst: React.FC = () => {
  const rightSidebarOpen = useSelector((state: any) => state.sidebar.rightSidebarOpen);
  const [selectedCategory, setSelectedCategory] = useState<ProductCategory | null>(null);

  const dispatch = useDispatch()

  const { id } = useParams<{ id: string }>();

  // Function to extract part between & symbols
 


  const param = id?.split('&')[1];
  console.log("extractedParam",param);


  const handleCategorySelect = (category: ProductCategory) => {
    console.log(category)
    setSelectedCategory(category);
    dispatch(setSelectedSubCat(category));
  };
  


const state=useSelector((state: any) => state.product)
console.log('state3',state)

// useEffect(()=>{
//   alert(state?.SelectedSubCat?.id)
//   setSelectedCategory(state?.SelectedSubCat)
// },[])



const routes = [
  { label: state?.SelectedCat?.label, path: '/sharp-source/buyer/product-list' },
  { label: selectedCategory?.label, path: `${`/sharp-source/buyer/product-list/${selectedCategory?.label||state?.SelectedSubCat?.label}`}` },
];

  return (
    <div style={{ padding: rightSidebarOpen ? '0 10px' : '0' }}>
      <SharpSourceNavbar title="" description=""  routes={routes}/>

      <div className="flex bg-backgroundFillColor w-[99%] rounded-b-xl rounded-tr-xl" style={{ borderTopLeftRadius: rightSidebarOpen ? '12px' : '0' }}>
        <div className="flex w-full">
          <ProductSubCategorySidebar handleCategorySelect={handleCategorySelect} ParentCategory={param || state?.allId?.subCategory} selected={state?.selectedSubCat} placeholder={selectedCategory?.label} />
          <ProductSummary subCategoryId={ state?.allId?.productId  } data={selectedCategory?.label||state?.SelectedSubCat?.label} />
        </div>
      </div>
    </div>
  );
};

export default ProductLisst;

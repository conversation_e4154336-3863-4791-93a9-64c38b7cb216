import ProtectedRoute from "@/components/ProtectedRoute";
import { SharpExchangeNavbar } from "@/components/shared/NavbarFactory";
import Appa from "@/components/Sidebar/Appa";
import Sidebar from "@/components/Welcome/Sidebar";
import { ReactNode } from "react";

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <ProtectedRoute>
      <div className="flex  bg-backgroundColor ">
        <Sidebar />
        <div className="my-2 mr-2 h-[calc(100vh-1rem)]  overflow-x-hidden  rounded-lg flex-1 hidden-overflow-x">
          {/* <Navbar/> */}
          {children}
        </div>
        <Appa />
      </div>
    </ProtectedRoute>
  );
};

export default Layout;

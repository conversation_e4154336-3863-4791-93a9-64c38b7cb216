const fs = require('fs');
const path = require('path');

// Files that need to be updated with their import replacements
const filesToUpdate = [
  {
    file: 'src/app/sharp-exchange/buyer/product-list/[id]/[Detail_ID]/page.tsx',
    oldImport: 'import Navbar from "@/components/SharpExchnage/Navbar";',
    newImport: 'import { SharpExchangeNavbar } from "@/components/shared/NavbarFactory";',
    oldUsage: '<Navbar',
    newUsage: '<SharpExchangeNavbar'
  },
  {
    file: 'src/components/SharpExchnage/Seller/Inventory/Dashboard.tsx',
    oldImport: 'import Navbar from "@/components/SharpExchnage/Navbar";',
    newImport: 'import { SharpExchangeNavbar } from "@/components/shared/NavbarFactory";',
    oldUsage: '<Navbar',
    newUsage: '<SharpExchangeNavbar'
  },
  {
    file: 'src/app/sharp-exchange/buyer/product-list/[id]/page.tsx',
    oldImport: 'import Navbar from "@/components/SharpExchnage/Navbar";',
    newImport: 'import { SharpExchangeNavbar } from "@/components/shared/NavbarFactory";',
    oldUsage: '<Navbar',
    newUsage: '<SharpExchangeNavbar'
  },
  {
    file: 'src/app/sharp-exchange/buyer/product-list/page.tsx',
    oldImport: 'import Navbar from "@/components/SharpExchnage/Navbar";',
    newImport: 'import { SharpExchangeNavbar } from "@/components/shared/NavbarFactory";',
    oldUsage: '<Navbar',
    newUsage: '<SharpExchangeNavbar'
  },
  {
    file: 'src/components/SharpSource/seller/Orders/Shippment-Details/Addess.tsx',
    oldImport: 'import Navbar from "@/components/SharpSource/Navbar";',
    newImport: 'import { SharpSourceNavbar } from "@/components/shared/NavbarFactory";',
    oldUsage: '<Navbar',
    newUsage: '<SharpSourceNavbar'
  },
  {
    file: 'src/components/SharpSource/buyer/RFQ-cart/Dashboard.tsx',
    oldImport: 'import Navbar from "@/components/SharpSource/Navbar";',
    newImport: 'import { SharpSourceNavbar } from "@/components/shared/NavbarFactory";',
    oldUsage: '<Navbar',
    newUsage: '<SharpSourceNavbar'
  },
  {
    file: 'src/components/SharpSource/buyer/logisticsData/logistics-detail/LogisticsDetail.tsx',
    oldImport: 'import Navbar from "@/components/SharpSource/Navbar";',
    newImport: 'import { SharpSourceNavbar } from "@/components/shared/NavbarFactory";',
    oldUsage: '<Navbar',
    newUsage: '<SharpSourceNavbar'
  },
  {
    file: 'src/app/sharp-source/buyer/product-list/[id]/[Detail_ID]/page.tsx',
    oldImport: 'import Navbar from "@/components/SharpSource/Navbar";',
    newImport: 'import { SharpSourceNavbar } from "@/components/shared/NavbarFactory";',
    oldUsage: '<Navbar',
    newUsage: '<SharpSourceNavbar'
  },
  {
    file: 'src/app/sharp-source/buyer/product-list/page.tsx',
    oldImport: 'import Navbar from "@/components/SharpSource/Navbar";',
    newImport: 'import { SharpSourceNavbar } from "@/components/shared/NavbarFactory";',
    oldUsage: '<Navbar',
    newUsage: '<SharpSourceNavbar'
  },
  {
    file: 'src/app/sharp-source/buyer/product-list/[id]/page.tsx',
    oldImport: 'import Navbar from "@/components/SharpSource/Navbar";',
    newImport: 'import { SharpSourceNavbar } from "@/components/shared/NavbarFactory";',
    oldUsage: '<Navbar',
    newUsage: '<SharpSourceNavbar'
  },
  {
    file: 'src/app/sharp-source/buyer/rfq-cart/VerifyData/page.tsx',
    oldImport: 'import Navbar from "@/components/SharpSource/Navbar";',
    newImport: 'import { SharpSourceNavbar } from "@/components/shared/NavbarFactory";',
    oldUsage: '<Navbar',
    newUsage: '<SharpSourceNavbar'
  },
  {
    file: 'src/components/SharpSource/buyer/logistics/logistics-detail/LogisticsDetail.tsx',
    oldImport: 'import Navbar from "@/components/SharpSource/Navbar";',
    newImport: 'import { SharpSourceNavbar } from "@/components/shared/NavbarFactory";',
    oldUsage: '<Navbar',
    newUsage: '<SharpSourceNavbar'
  }
];

// Function to update a single file
function updateFile(fileInfo) {
  const filePath = fileInfo.file;
  
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.log(`File not found: ${filePath}`);
      return;
    }

    // Read the file
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Replace the import statement
    if (content.includes(fileInfo.oldImport)) {
      content = content.replace(fileInfo.oldImport, fileInfo.newImport);
      console.log(`Updated import in: ${filePath}`);
    }
    
    // Replace the usage (component name)
    const usageRegex = new RegExp(fileInfo.oldUsage.replace('<', '<'), 'g');
    if (content.match(usageRegex)) {
      content = content.replace(usageRegex, fileInfo.newUsage);
      console.log(`Updated usage in: ${filePath}`);
    }
    
    // Write the file back
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Successfully updated: ${filePath}`);
    
  } catch (error) {
    console.error(`Error updating ${filePath}:`, error.message);
  }
}

// Update all files
console.log('Starting navbar import fixes...');
filesToUpdate.forEach(updateFile);
console.log('Navbar import fixes completed!');
